// 三维跳点搜索算法实现
#pragma once

#include "GridNode3D.h"   // 引入三维栅格节点（包含x,y,z,t,g,h,f,parent,jump_step,need_sight属性）
#include "Map3D.h"        // 引入三维地图
#include "OccupancyMap.h" // 引入占用图
// #include "Constraint.h" // 约束条件类（如果需要）

#include <vector>         // 用于存储路径点
#include <string>         // 用于字符串处理
#include <optional>       // 用于可选返回值
#include <map>            // 用于路径映射
#include <tuple>          // 用于方向和位置表示
#include <stdexcept>      // 用于异常处理
#include <functional>     // 用于启发式函数对象
#include <unordered_set>  // 用于高效的集合操作
#include "common_types.h" // 引入通用类型定义（包含Point3DHash）

// 约束条件类的前向声明（如果需要）
// struct Constraint;

class JPS
{
public:
    using Direction3D = std::tuple<int, int, int>; // 方向向量(dx,dy,dz)
    using Point3D = std::tuple<int, int, int>;     // 位置坐标(x,y,z)

    // 跳点搜索算法构造函数
    // @param map_data: 三维地图指针
    // @param occupancy_map_data: 占用图指针
    // @param takeoff_speed: 起飞速度
    // @param cruise_speed: 巡航速度
    // @param landing_speed: 降落速度
    // @param max_steps: 最大搜索步数
    // @param need_smooth: 是否需要路径平滑
    // @param smoothness: 平滑程度
    // @param heuristic_weight: 启发式函数权重（默认值2.0）
    // @param jump_step_size: 跳跃步长（默认值5）
    JPS(Map3D *map_data,
        OccupancyMap *occupancy_map_data,
        double takeoff_speed,
        double cruise_speed,
        double landing_speed,
        int max_steps,
        bool need_smooth,
        double smoothness,
        double heuristic_weight = 2.0,
        int jump_step_size = 5);

    // 寻找从起点到终点的可行路径
    // @param start_node_args: 起始节点
    // @param goal_node_args: 目标节点
    // @param min_height: 最小飞行高度
    // @param agent_id: 飞行器ID
    // @param path_start_time: 路径开始时间
    // @return: 返回一个元组，包含：
    //          - complete_path: 完整路径点序列（如果找到）
    //          - complete_turn_path: 转弯点序列（如果适用）
    //          - error_message: 错误信息（如果有）
    std::tuple<
        std::optional<std::vector<GridNode3D>>, // 完整路径
        std::optional<std::vector<GridNode3D>>, // 转弯点路径
        std::optional<std::string>              // 错误信息
        >
    find_path(const GridNode3D &start_node_args,
              const GridNode3D &goal_node_args,
              int min_height,
              const std::string &agent_id,
              long long path_start_time
              // const std::vector<Constraint>& constraints = {} // 可选的约束条件
    );

    // 搜索方向优先级列表
    static const std::vector<Direction3D> DIRECTION_PRIORITIES;
    // 强制邻居方向映射表
    static const std::map<Direction3D, std::vector<Direction3D>> FORCED_NEIGHBOR_DIRS;

    // 优化：分离的方向常量，避免循环中的条件检查
    static constexpr int HORIZONTAL_DIRECTIONS[][3] = {
        // 第一优先级：XY平面基本方向（水平移动）
        {1, 0, 0},  // 向右移动
        {-1, 0, 0}, // 向左移动
        {0, 1, 0},  // 向前移动
        {0, -1, 0}, // 向后移动
        // 第二优先级：XY平面对角线移动（斜向移动）
        {1, 1, 0},  // 右前移动
        {-1, 1, 0}, // 左前移动
        {1, -1, 0}, // 右后移动
        {-1, -1, 0} // 左后移动
    };

    static constexpr int VERTICAL_DIRECTIONS[][3] = {
        {0, 0, 1}, // 垂直向上
        {0, 0, -1} // 垂直向下
    };

    static constexpr size_t HORIZONTAL_DIR_COUNT = sizeof(HORIZONTAL_DIRECTIONS) / sizeof(HORIZONTAL_DIRECTIONS[0]);
    static constexpr size_t VERTICAL_DIR_COUNT = sizeof(VERTICAL_DIRECTIONS) / sizeof(VERTICAL_DIRECTIONS[0]);

private:
    // 核心组件
    Map3D *map_;                  // 三维地图指针
    OccupancyMap *occupancy_map_; // 占用图指针
    // const std::vector<Constraint>* current_constraints_; // 当前路径规划的约束条件

    // 地图尺寸
    int width_;  // 地图宽度
    int height_; // 地图高度
    int depth_;  // 地图深度

    // 速度参数（时间单位）
    double takeoff_speed_t_; // 起飞速度
    double cruise_speed_t_;  // 巡航速度
    double landing_speed_t_; // 降落速度

    // 路径规划参数
    int max_steps_;              // 最大搜索步数
    bool need_smooth_;           // 是否需要平滑处理
    double smoothness_;          // 平滑程度
    double heuristic_weight_;    // 启发式函数权重
    int default_jump_step_size_; // 默认跳跃步长

    // --- 核心路径规划阶段 ---

    // 垂直起飞阶段路径规划
    // @param start_pos: 起飞位置坐标
    // @param min_height: 最小飞行高度
    // @param agent_id: 飞行器ID
    // @param current_time: 起飞开始时间
    // @return: 返回起飞路径和可能的错误信息
    std::tuple<std::optional<std::vector<GridNode3D>>, std::optional<std::string>>
    _vertical_takeoff(
        const Point3D &start_pos,
        int min_height,
        const std::string &agent_id,
        long long current_time
        // const std::vector<Constraint>& constraints
    );

    // 巡航阶段路径规划
    // @param start_node: 起始节点（起飞结束位置）
    // @param goal_pos: 目标位置（降落起始位置）
    // @param min_height: 最小飞行高度
    // @param agent_id: 飞行器ID
    // @param cruise_start_time: 巡航开始时间
    // @return: 返回巡航路径和错误信息（参考Python实现）
    std::tuple<
        std::optional<std::vector<GridNode3D>>, // 路径节点序列
        std::optional<std::string>              // 错误信息
        >
    _find_cruise_path(
        const GridNode3D &start_node,
        const Point3D &goal_pos,
        int min_height,
        const std::string &agent_id,
        long long cruise_start_time
        // const std::vector<Constraint>& constraints
    );

    // 垂直降落阶段路径规划
    // @param last_cruise_node: 巡航结束节点
    // @param goal_pos: 降落目标位置
    // @param agent_id: 飞行器ID
    // @return: 返回降落路径和可能的错误信息
    std::tuple<std::optional<std::vector<GridNode3D>>, std::optional<std::string>>
    _vertical_landing(
        const GridNode3D &last_cruise_node,
        const Point3D &goal_pos,
        const std::string &agent_id
        // const std::vector<Constraint>& constraints
    );

    // --- 跳点搜索基础组件 ---

    // 表示邻居节点的一系列点，特别是在垂直避让时
    using NeighborPath = std::vector<Point3D>;

    // 获取当前节点的邻居节点列表
    // @param current_node: 当前节点
    // @param min_height: 最小飞行高度
    // @param agent_id: 飞行器ID
    // @return: 返回可行的邻居节点路径列表
    std::vector<NeighborPath> _get_neighbors(
        const GridNode3D &current_node,
        int min_height,
        const std::string &agent_id
        // const std::vector<Constraint>& constraints
    );

    // 从当前节点进行跳点搜索
    // @param current_node: 当前节点
    // @param parent_node: 父节点（用于确定搜索方向）
    // @param goal_pos: 搜索目标位置
    // @param min_height: 最小飞行高度
    // @param agent_id: 飞行器ID
    // @param jump_step: 跳跃步长
    // @return: 返回找到的跳点节点（如果存在）
    std::optional<GridNode3D> _jump(
        const GridNode3D &current_node,
        const GridNode3D &parent_node,
        const Point3D &goal_pos,
        int min_height,
        const std::string &agent_id,
        int jump_step
        // const std::vector<Constraint>& constraints
    );

    // --- 工具和辅助方法 ---

    // 检查约束和碰撞情况
    // @param pos: 待检查位置
    // @param time: 检查时间点
    // @param agent_id: 飞行器ID
    // @return: 返回<是否有效, 错误信息>
    std::pair<bool, std::optional<std::string>> _check_constraints_and_collisions(
        const Point3D &pos,
        long long time,
        const std::string &agent_id
        // const std::vector<Constraint>& constraints
    );

    // 检查位置是否有效
    // @param pos: 待检查位置
    // @param time: 检查时间点
    // @param min_height: 最小飞行高度
    // @param agent_id: 飞行器ID
    // @param ignore_min_height: 是否忽略最小高度限制
    // @return: 返回<是否有效, 错误信息>
    std::pair<bool, std::optional<std::string>> _is_valid_position(
        const Point3D &pos,
        long long time,
        int min_height,
        const std::string &agent_id,
        // const std::vector<Constraint>& constraints,
        bool ignore_min_height = false);

    // 计算启发式估值
    double _heuristic(const Point3D &p, const Point3D &goal) const;

    // 计算八方向距离
    double _octile_distance(const Point3D &p1, const Point3D &p2) const;

    // 计算欧氏距离的平方（用于视线检查）
    double _euclidean_distance_squared(const Point3D &p1, const Point3D &p2) const;

    // 视线可达性检查
    // @param start_node_los: 视线检查起点节点
    // @param end_pos_los: 视线检查终点位置
    // @param los_start_time: 检查开始时间
    // @param min_height: 最小飞行高度
    // @param agent_id: 飞行器ID
    // @return: 返回<是否可达, 可达路径点序列或最后的有效点>
    std::pair<bool, std::vector<GridNode3D>> has_line_of_sight(
        const GridNode3D &start_node_los,
        const Point3D &end_pos_los,
        long long los_start_time,
        int min_height,
        const std::string &agent_id
        // const std::vector<Constraint>& constraints
    );

    // --- 路径后处理方法 ---

    // 提取路径中的转弯点
    // @param path: 原始路径点序列
    // @return: 返回<处理后的路径, 转弯点索引>
    std::pair<std::vector<GridNode3D>, std::vector<int>> extract_turning_points(
        const std::vector<GridNode3D> &path);

    // 移动平均平滑处理
    // @param path: 待平滑的路径点序列
    // @param turning_indices: 转弯点在原始路径中的索引
    // @param min_height: 最小飞行高度
    // @param agent_id: 飞行器ID
    // @return: 返回<平滑后的路径, 平滑后的转弯点>
    std::pair<std::vector<GridNode3D>, std::vector<GridNode3D>> moving_average_smooth(
        const std::vector<GridNode3D> &path,
        const std::vector<int> &turning_indices,
        int min_height,
        const std::string &agent_id
        // const std::vector<Constraint>& constraints
    );

    // 节点类型转换辅助函数
    // 将GridNode3D节点转换为Point3D坐标
    Point3D _node_to_point3d(const GridNode3D &node) const
    {
        return {node.x, node.y, node.z};
    }
};
