#include "cpp_implementation/include/Map3D.h"
#include "cpp_implementation/include/GridConverter.h"
#include <iostream>
#include <cassert>

int main() {
    // 创建一个简单的网格转换器
    GridConverter converter(39.9, 116.4, 100.0, 100.0, 10, 10);
    
    // 创建一个10x10x5的地图
    Map3D map(10, 10, 5, converter);
    
    std::cout << "测试开始：检查二维点是否在禁飞区内" << std::endl;
    
    // 测试1：添加一个圆柱形禁飞区
    std::cout << "\n测试1：圆柱形禁飞区" << std::endl;
    std::map<std::string, std::vector<GridNode3D>> empty_paths;
    
    auto result1 = map.add_solid_cylindrical_no_fly_zone_grid(
        5.0f, 5.0f,  // 中心在(5, 5)
        2.0f,        // 半径为2
        "test_cylinder",
        empty_paths,
        0            // 实心圆柱体
    );
    
    if (result1.first) {
        std::cout << "圆柱形禁飞区添加成功" << std::endl;
        
        // 测试圆柱体内部的点
        std::string result = map.is_point_inside_any_nfz_2d({5, 5}); // 中心点
        std::cout << "点(5,5)检查结果: " << (result.empty() ? "不在禁飞区" : "在禁飞区: " + result) << std::endl;
        
        result = map.is_point_inside_any_nfz_2d({6, 5}); // 半径内的点
        std::cout << "点(6,5)检查结果: " << (result.empty() ? "不在禁飞区" : "在禁飞区: " + result) << std::endl;
        
        result = map.is_point_inside_any_nfz_2d({8, 5}); // 半径外的点
        std::cout << "点(8,5)检查结果: " << (result.empty() ? "不在禁飞区" : "在禁飞区: " + result) << std::endl;
        
        result = map.is_point_inside_any_nfz_2d({0, 0}); // 远离的点
        std::cout << "点(0,0)检查结果: " << (result.empty() ? "不在禁飞区" : "在禁飞区: " + result) << std::endl;
    } else {
        std::cout << "圆柱形禁飞区添加失败" << std::endl;
    }
    
    // 测试2：添加一个多边形禁飞区
    std::cout << "\n测试2：多边形禁飞区" << std::endl;
    std::vector<std::pair<float, float>> polygon_vertices = {
        {1.0f, 1.0f},
        {3.0f, 1.0f},
        {3.0f, 3.0f},
        {1.0f, 3.0f}
    };
    
    auto result2 = map.add_hollow_polygonal_no_fly_zone_grid(
        polygon_vertices,
        "test_polygon",
        0.0f,        // 无偏移
        1,           // 边界厚度为1
        empty_paths
    );
    
    if (result2.first) {
        std::cout << "多边形禁飞区添加成功" << std::endl;
        
        // 测试多边形内部的点
        std::string result = map.is_point_inside_any_nfz_2d({2, 2}); // 内部点
        std::cout << "点(2,2)检查结果: " << (result.empty() ? "不在禁飞区" : "在禁飞区: " + result) << std::endl;
        
        result = map.is_point_inside_any_nfz_2d({4, 4}); // 外部点
        std::cout << "点(4,4)检查结果: " << (result.empty() ? "不在禁飞区" : "在禁飞区: " + result) << std::endl;
        
        result = map.is_point_inside_any_nfz_2d({1, 1}); // 边界点
        std::cout << "点(1,1)检查结果: " << (result.empty() ? "不在禁飞区" : "在禁飞区: " + result) << std::endl;
    } else {
        std::cout << "多边形禁飞区添加失败" << std::endl;
    }
    
    // 测试3：边界检查
    std::cout << "\n测试3：边界检查" << std::endl;
    std::string result = map.is_point_inside_any_nfz_2d({-1, 5}); // 超出边界
    std::cout << "点(-1,5)检查结果: " << (result.empty() ? "不在禁飞区" : "在禁飞区: " + result) << std::endl;
    
    result = map.is_point_inside_any_nfz_2d({15, 5}); // 超出边界
    std::cout << "点(15,5)检查结果: " << (result.empty() ? "不在禁飞区" : "在禁飞区: " + result) << std::endl;
    
    std::cout << "\n测试完成！" << std::endl;
    return 0;
}
