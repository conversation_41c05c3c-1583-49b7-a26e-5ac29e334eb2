2025-05-30 15:16:17,747 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:16:17,748 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:16:17,748 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:16:17,748 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:16:17,782 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:16:17,783 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:16:17,784 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:16:17,784 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:16:17,784 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:16:17,785 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:16:17,785 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:16:17,785 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:16:17,787 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:16:17,792 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:16:17,919 - src.utils.unified_timer_manager - INFO - 正在关闭统一定时器管理器...
2025-05-30 15:16:18,815 - src.utils.unified_timer_manager - INFO - 统一定时器循环已退出
2025-05-30 15:16:18,816 - src.utils.unified_timer_manager - INFO - 定时器线程已正常结束
2025-05-30 15:16:18,816 - src.utils.unified_timer_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:16:18,817 - src.utils.db_connection_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:16:18,818 - src.utils.db_connection_manager - INFO - 自动路径规划数据库连接已关闭
2025-05-30 15:16:18,818 - src.utils.db_connection_manager - INFO - 数据库连接管理器线程池已关闭
2025-05-30 15:16:18,818 - src.utils.db_connection_manager - INFO - 关闭时发现 0 个连接池
2025-05-30 15:16:18,818 - src.utils.db_connection_manager - INFO - 已清空所有连接池字典
2025-05-30 15:16:19,324 - src.handlers.message_handlers.base - INFO - 数据库连接管理器已关闭
2025-05-30 15:16:19,803 - src.handlers.message_handlers.base - INFO - MQTT客户端已关闭
2025-05-30 15:18:00,204 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:18:00,204 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:18:00,205 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:18:00,205 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:18:00,236 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:18:00,238 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:18:00,239 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:18:00,239 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:18:00,239 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:18:00,240 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:18:00,240 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:18:00,240 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:18:00,241 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:18:00,259 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:18:15,145 - src.utils.unified_timer_manager - INFO - 正在关闭统一定时器管理器...
2025-05-30 15:18:15,150 - src.utils.unified_timer_manager - INFO - 统一定时器循环已退出
2025-05-30 15:18:15,151 - src.utils.unified_timer_manager - INFO - 定时器线程已正常结束
2025-05-30 15:18:15,152 - src.utils.unified_timer_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:18:15,157 - src.utils.db_connection_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:18:15,158 - src.utils.db_connection_manager - INFO - 自动路径规划数据库连接已关闭
2025-05-30 15:18:15,158 - src.utils.db_connection_manager - INFO - 数据库连接管理器线程池已关闭
2025-05-30 15:18:15,158 - src.utils.db_connection_manager - INFO - 关闭时发现 0 个连接池
2025-05-30 15:18:15,158 - src.utils.db_connection_manager - INFO - 已清空所有连接池字典
2025-05-30 15:18:15,659 - src.handlers.message_handlers.base - INFO - 数据库连接管理器已关闭
2025-05-30 15:18:16,157 - src.handlers.message_handlers.base - INFO - MQTT客户端已关闭
2025-05-30 15:18:32,465 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:18:32,465 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:18:32,465 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:18:32,465 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:18:32,518 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:18:32,520 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:18:32,520 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:18:32,520 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:18:32,520 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:18:32,521 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:18:32,521 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:18:32,521 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:18:32,521 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:18:32,548 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:21:27,331 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:21:27,332 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:21:27,332 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:21:27,332 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:21:27,381 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:21:27,383 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:21:27,383 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:21:27,384 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:21:27,384 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:21:27,384 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:21:27,385 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:21:27,385 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:21:27,385 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:21:27,413 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:21:53,601 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:21:53,603 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:21:53,603 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:21:53,603 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:21:53,654 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:21:53,656 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:21:53,656 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:21:53,657 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:21:53,657 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:21:53,657 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:21:53,658 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:21:53,658 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:21:53,658 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:21:53,686 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:23:00,509 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:23:00,509 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:23:00,509 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:23:00,509 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:23:00,545 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:23:00,548 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:23:00,548 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:23:00,549 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:23:00,549 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:23:00,549 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:23:00,549 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:23:00,549 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:23:00,550 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:23:00,569 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:32:19,488 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:32:19,489 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:32:19,489 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:32:19,489 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:32:19,538 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:32:19,540 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:32:19,540 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:32:19,540 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:32:19,541 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:32:19,541 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:32:19,541 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:32:19,541 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:32:19,542 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:32:19,551 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:47:05,504 - src.utils.unified_timer_manager - INFO - 正在关闭统一定时器管理器...
2025-05-30 15:47:05,627 - src.utils.unified_timer_manager - INFO - 统一定时器循环已退出
2025-05-30 15:47:05,628 - kafka.client - WARNING - <BrokerConnection node_id=bootstrap-0 host=***********:9093 <connected> [IPv4 ('***********', 9093)]> timed out after 30000 ms. Closing connection.
2025-05-30 15:47:05,628 - src.utils.unified_timer_manager - INFO - 定时器线程已正常结束
2025-05-30 15:47:05,630 - src.utils.unified_timer_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:47:05,631 - src.utils.db_connection_manager - INFO - 统一定时器管理器已关闭
2025-05-30 15:47:05,633 - src.utils.db_connection_manager - INFO - 自动路径规划数据库连接已关闭
2025-05-30 15:47:05,634 - src.utils.db_connection_manager - INFO - 数据库连接管理器线程池已关闭
2025-05-30 15:47:05,635 - src.utils.db_connection_manager - INFO - 关闭时发现 0 个连接池
2025-05-30 15:47:05,635 - src.utils.db_connection_manager - INFO - 已清空所有连接池字典
2025-05-30 15:47:06,151 - src.handlers.message_handlers.base - INFO - 数据库连接管理器已关闭
2025-05-30 15:47:06,495 - src.handlers.message_handlers.base - INFO - MQTT客户端已关闭
2025-05-30 15:54:48,843 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 15:54:48,844 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 15:54:48,844 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 15:54:48,844 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 15:54:48,885 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 15:54:48,887 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 15:54:48,887 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 15:54:48,887 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 15:54:48,887 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 15:54:48,887 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 15:54:48,888 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 15:54:48,888 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 15:54:48,888 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 15:54:48,893 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 15:55:40,319 - kafka.client - WARNING - <BrokerConnection node_id=bootstrap-0 host=***********:9093 <connected> [IPv4 ('***********', 9093)]> timed out after 30000 ms. Closing connection.
2025-05-30 15:55:42,530 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 15:55:47,352 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 15:55:47,353 - src.handlers.message_handlers.base - ERROR - 处理禁飞区 测试 时出错: 'pathfinding_cpp.Map3D' object has no attribute 'add_solid_polygon_no_fly_zone'
2025-05-30 16:15:56,322 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:15:56,323 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:15:56,323 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:15:56,323 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:15:56,413 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:15:56,414 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:15:56,414 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:15:56,414 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:15:56,415 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:15:56,415 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:15:56,415 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:15:56,415 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:15:56,415 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:15:56,446 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:16:06,342 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:29:57,680 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:29:57,680 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:29:57,680 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:29:57,680 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:29:57,735 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:29:57,737 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:29:57,737 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:29:57,737 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:29:57,738 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:29:57,738 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:29:57,738 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:29:57,738 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:29:57,738 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:29:57,765 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:30:04,789 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:30:04,819 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 16:41:12,127 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:41:12,128 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:41:12,128 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:41:12,128 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:41:12,170 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:41:12,172 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:41:12,172 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:41:12,172 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:41:12,173 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:41:12,173 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:41:12,173 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:41:12,173 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:41:12,174 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:41:12,204 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:41:23,756 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:41:23,786 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 16:41:38,381 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-05-30 16:41:38,439 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-05-30 16:41:38,443 - src.utils.unified_timer_manager - INFO - 正在关闭统一定时器管理器...
2025-05-30 16:41:39,394 - src.utils.unified_timer_manager - INFO - 统一定时器循环已退出
2025-05-30 16:41:39,395 - src.utils.unified_timer_manager - INFO - 定时器线程已正常结束
2025-05-30 16:41:39,395 - src.utils.unified_timer_manager - INFO - 统一定时器管理器已关闭
2025-05-30 16:41:39,395 - src.utils.db_connection_manager - INFO - 统一定时器管理器已关闭
2025-05-30 16:41:39,396 - src.utils.db_connection_manager - INFO - 自动路径规划数据库连接已关闭
2025-05-30 16:41:39,396 - src.utils.db_connection_manager - INFO - 数据库连接管理器线程池已关闭
2025-05-30 16:41:39,396 - src.utils.db_connection_manager - INFO - 关闭时发现 0 个连接池
2025-05-30 16:41:39,397 - src.utils.db_connection_manager - INFO - 已清空所有连接池字典
2025-05-30 16:41:39,903 - src.handlers.message_handlers.base - INFO - 数据库连接管理器已关闭
2025-05-30 16:41:39,905 - src.handlers.message_handlers.base - INFO - Kafka生产者已关闭
2025-05-30 16:41:40,398 - src.handlers.message_handlers.base - INFO - MQTT客户端已关闭
2025-05-30 16:42:30,419 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:42:30,420 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:42:30,420 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:42:30,420 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:42:30,469 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:42:30,470 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:42:30,471 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:42:30,471 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:42:30,471 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:42:30,471 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:42:30,471 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:42:30,472 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:42:30,472 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:42:30,499 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:42:34,112 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:42:34,140 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 16:43:18,346 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-05-30 16:43:31,248 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-05-30 16:43:31,249 - src.utils.unified_timer_manager - INFO - 正在关闭统一定时器管理器...
2025-05-30 16:43:31,294 - src.utils.unified_timer_manager - INFO - 统一定时器循环已退出
2025-05-30 16:43:31,295 - src.utils.unified_timer_manager - INFO - 定时器线程已正常结束
2025-05-30 16:43:31,295 - src.utils.unified_timer_manager - INFO - 统一定时器管理器已关闭
2025-05-30 16:43:31,295 - src.utils.db_connection_manager - INFO - 统一定时器管理器已关闭
2025-05-30 16:43:31,296 - src.utils.db_connection_manager - INFO - 自动路径规划数据库连接已关闭
2025-05-30 16:43:31,296 - src.utils.db_connection_manager - INFO - 数据库连接管理器线程池已关闭
2025-05-30 16:43:31,297 - src.utils.db_connection_manager - INFO - 关闭时发现 0 个连接池
2025-05-30 16:43:31,297 - src.utils.db_connection_manager - INFO - 已清空所有连接池字典
2025-05-30 16:44:12,049 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:44:12,049 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:44:12,050 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:44:12,050 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:44:12,104 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:44:12,105 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:44:12,106 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:44:12,107 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:44:12,107 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:44:12,107 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:44:12,107 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:44:12,107 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:44:12,108 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:44:12,118 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:44:32,593 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:44:32,744 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 16:44:39,315 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-05-30 16:44:39,324 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-05-30 16:46:50,714 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-05-30 16:46:50,715 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-05-30 16:46:50,715 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-05-30 16:46:50,715 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-05-30 16:46:50,760 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-05-30 16:46:50,761 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-05-30 16:46:50,762 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-05-30 16:46:50,762 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-05-30 16:46:50,762 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-05-30 16:46:50,762 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-05-30 16:46:50,763 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-05-30 16:46:50,763 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-05-30 16:46:50,763 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-05-30 16:46:50,769 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-05-30 16:46:57,792 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-05-30 16:46:57,821 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-05-30 16:47:04,649 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-05-30 16:47:04,661 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-05-30 16:48:22,925 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-05-30 16:48:23,513 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-05-30 16:48:24,534 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-05-30 16:48:25,011 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-05-30 16:48:25,011 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-05-30 16:48:25,154 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-05-30 16:48:25,176 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-05-30 16:48:25,176 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-05-30 16:48:26,730 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-05-30 16:48:26,873 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-05-30 16:48:26,873 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-05-30 16:48:27,019 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-05-30 16:48:27,022 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-05-30 16:48:27,023 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-05-30 16:48:28,001 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-05-30 16:48:28,002 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-05-30 16:48:28,002 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-05-30 16:48:28,003 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-05-30 16:48:28,003 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-05-30 16:48:28,004 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-05-30 16:48:30,122 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-05-30 16:48:30,123 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-05-30 16:48:31,143 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-05-30 16:48:31,143 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-05-30 16:48:33,167 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 08:53:38,421 - src.config.settings - INFO - 尝试从 config.json 加载配置
2025-06-03 08:53:38,421 - src.config.settings - INFO - 成功从 config.json 加载配置
2025-06-03 08:53:38,421 - src.config.settings - INFO - 使用配置文件中的地点: nanjing
2025-06-03 08:53:38,421 - src.config.settings - INFO - 使用配置文件中的服务器地址: ***********
2025-06-03 08:53:38,516 - src.utils.unified_timer_manager - INFO - 任务 db_conn_check 已注册
2025-06-03 08:53:38,516 - src.utils.unified_timer_manager - INFO - 统一定时器线程已启动，间隔: 60秒
2025-06-03 08:53:38,516 - src.utils.db_connection_manager - INFO - 已注册数据库连接检查任务
2025-06-03 08:53:38,518 - src.utils.unified_timer_manager - INFO - 任务 db_pool_cleanup 已注册
2025-06-03 08:53:38,518 - src.utils.unified_timer_manager - INFO - 统一定时器循环开始运行
2025-06-03 08:53:38,518 - src.utils.db_connection_manager - INFO - 已注册连接池清理任务
2025-06-03 08:53:38,518 - src.utils.unified_timer_manager - INFO - 任务 db_conn_keepalive 已注册
2025-06-03 08:53:38,518 - src.utils.db_connection_manager - INFO - 已注册数据库连接保活任务
2025-06-03 08:53:38,518 - src.handlers.message_handlers.base - INFO - 系统启动时初始化数据库连接管理器成功
2025-06-03 08:53:38,549 - src.handlers.message_handlers.base - INFO - 系统启动时初始化MQTT客户端成功
2025-06-03 08:53:42,212 - src.handlers.message_handlers.base - INFO - 开始从数据库加载禁飞区信息...
2025-06-03 08:53:42,274 - src.handlers.message_handlers.base - INFO - 从数据库中找到 1 个禁飞区
2025-06-03 08:53:48,753 - src.handlers.message_handlers.base - INFO - 成功加载多边形禁飞区: 测试, 顶点数: 6
2025-06-03 08:53:48,805 - src.handlers.message_handlers.base - INFO - 禁飞区加载完成
2025-06-03 08:53:48,805 - src.handlers.risk_assessment_consumer - INFO - 已初始化统一定时器管理器，间隔: 60秒
2025-06-03 08:53:48,805 - src.handlers.risk_assessment_consumer - INFO - Redis连接初始化成功
2025-06-03 08:53:52,778 - src.handlers.risk_assessment_consumer - INFO - 尝试连接Kafka服务器: ['***********:9093'] (尝试 1/5)
2025-06-03 08:53:54,208 - src.handlers.risk_assessment_consumer - INFO - Kafka服务器连接测试成功
2025-06-03 08:53:54,208 - src.handlers.risk_assessment_consumer - INFO - 初始化飞行监控消费者...
2025-06-03 08:53:54,339 - src.handlers.risk_assessment_consumer - INFO - 测试飞行监控消费者连接...
2025-06-03 08:53:54,348 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者连接测试成功
2025-06-03 08:53:54,348 - src.handlers.risk_assessment_consumer - INFO - Kafka连接初始化成功
2025-06-03 08:53:54,349 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化状态机消费者 (尝试 1/5)
2025-06-03 08:53:54,482 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者初始化成功，监听主题: flighttask_method
2025-06-03 08:53:54,482 - src.handlers.risk_assessment_consumer - INFO - 尝试初始化Kafka生产者 (尝试 1/5)
2025-06-03 08:53:54,608 - src.handlers.risk_assessment_consumer - INFO - 测试Kafka生产者连接...
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - Kafka生产者连接测试成功
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者和生产者初始化成功
2025-06-03 08:53:54,611 - src.utils.unified_timer_manager - INFO - 任务 redis_conn_check 已注册
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 已注册Redis连接检查任务
2025-06-03 08:53:54,611 - src.utils.unified_timer_manager - INFO - 任务 path_cleanup 已注册
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 已注册路径清理任务
2025-06-03 08:53:54,611 - src.utils.unified_timer_manager - INFO - 任务 kafka_heartbeat 已注册
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 已注册Kafka心跳检查任务
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 状态机消费者线程已启动
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 开始监听状态机消息...
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 飞行监控消费者线程已启动
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 开始监听飞行监控消息...
2025-06-03 08:53:54,611 - src.handlers.risk_assessment_consumer - INFO - 风险评估服务已启动，等待消息处理...
2025-06-03 08:55:35,116 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 08:55:35,116 - src.handlers.risk_assessment_consumer - INFO - [36m收到状态机消息: 待提交 -> 风险评估[0m
2025-06-03 08:55:35,165 - src.handlers.risk_assessment_consumer - INFO - [36m收到消息: {'id': '549b50c7-ccbc-49a1-ad4a-326f992d677d', 'module': 'input', 'source': '待提交', 'desc': '风险评估', 'timestamp': 1748912194765, 'mode_code': 'RISK', 'data': {'id': '549b50c7-ccbc-49a1-ad4a-326f992d677d', 'flightapplyid': '549b50c7-ccbc-49a1-ad4a-326f992d677d', 'flyHeight': 390.0, 'flyMode': '自主飞行', 'flySecure': '', 'immeProc': '', 'landingPoint': '118.57506922291748,32.064527617814946,180.0', 'mission': '巡检', 'operator': 'o-001', 'operatorId': '孙鹏远', 'operatorsMode': '视距内飞行', 'takeOffPoint': '118.62287691639892,32.13705454957764,180.0', 'device_sn': 'MD2407011PV000', 'gateway_sn': '7CTDM3D00B2792', 'uavroot': '青岛_自动规划路径 ', 'uavrootId': '', 'zoneId': '', 'zoneName': '', 'beginTime': '2025-06-03 08:56:34', 'commWay': '', 'endTime': '2025-06-03 09:56:34', 'task_source': '0'}}[0m
2025-06-03 08:55:35,171 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 删除旧路径耗时: 0.0000 秒
2025-06-03 08:55:35,171 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 解析起降点耗时: 0.0000 秒
2025-06-03 08:55:35,171 - src.handlers.message_handlers.planning_handler - ERROR - 处理路径规划消息时出错: 'pathfinding_cpp.GridConverter' object has no attribute 'height_to_relative'
2025-06-03 08:55:35,171 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - 总体执行时间(异常): 0.0065 秒
2025-06-03 08:55:35,171 - src.handlers.message_handlers.planning_handler - INFO - 性能统计 - handle_message 执行时间: 0.0065 秒
2025-06-03 08:55:35,196 - src.handlers.risk_assessment_consumer - INFO - [32m已发送消息到flighttask_method: {'bid': '508a0fef-24e0-4b12-a9a9-0b73f9ccd873', 'id': '549b50c7-ccbc-49a1-ad4a-326f992d677d', 'module': 'risk', 'source': '风险评估', 'timestamp': 1748912135, 'data': {'risk_state': False, 'risk_reason': "处理路径规划消息时出错: 'pathfinding_cpp.GridConverter' object has no attribute 'height_to_relative'"}, 'desc': '待调整'}[0m
2025-06-03 08:55:35,474 - src.handlers.risk_assessment_consumer - INFO - 使用顺序处理
2025-06-03 09:04:43,387 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:05:38,951 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:06:38,773 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:07:38,730 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:08:38,717 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:09:38,880 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:10:38,716 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:11:38,664 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:12:38,707 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:13:38,709 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:14:38,711 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:15:38,719 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:16:38,788 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:17:38,994 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:18:38,737 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:19:38,738 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:20:38,772 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:21:38,749 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:22:38,758 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:23:38,772 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:24:38,783 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:25:38,820 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:26:38,822 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:27:38,811 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:28:38,823 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:29:38,814 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:30:38,867 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
2025-06-03 09:31:38,858 - src.handlers.risk_assessment_consumer - ERROR - 清理已完成航班路径时出错: 'pathfinding_cpp.Map3D' object has no attribute 'obstacle_manager'
