// main.cpp
#include <iostream>
#include <vector>
#include <string>
#include <map>
#include <iomanip>
#include <cmath> // 添加 cmath 头文件，用于 cos 函数

// 定义 M_PI 常量（如果编译器没有提供）
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#include "Map3D.h"
#include "GridConverter.h"
#include "GridNode3D.h"
#include "common_types.h"
#include "OccupancyMap.h" // 添加 OccupancyMap 头文件
#include "JPS.h"          // 添加 JPS 头文件

// 测试辅助函数
void print_separator(const std::string &title)
{
    std::cout << "\n"
              << std::string(50, '=') << "\n";
    std::cout << "  " << title << "\n";
    std::cout << std::string(50, '=') << "\n";
}

void print_result(const std::string &test_name, bool success)
{
    std::cout << std::setw(40) << std::left << test_name;
    std::cout << ": " << (success ? "成功" : "失败") << std::endl;
}

static void test_map3d()
{
    print_separator("Map3D 功能测试");

    // 创建 GridConverter 实例
    GridCellSize cell_size = {0.0001, 0.0001, 1.0}; // 约 11m x 11m 的栅格
    GeoCoordinate min_coords = {30.0, 120.0, 0.0};  // 地图左下角坐标
    GridConverter converter(cell_size, min_coords);

    // 创建 Map3D 实例
    int height = 100; // 栅格高度 (y 方向)
    int width = 100;  // 栅格宽度 (x 方向)
    int depth = 10;   // 栅格深度 (z 方向)
    Map3D map(height, width, depth, converter);

    std::cout << "创建了 " << width << "x" << height << "x" << depth << " 的 Map3D 实例\n";

    // 测试 1: 基本属性获取
    print_separator("测试 1: 基本属性获取");
    bool test1_success = true;

    test1_success &= (map.get_height() == height); // &= 逻辑与赋值
    test1_success &= (map.get_width() == width);
    test1_success &= (map.get_depth() == depth);

    std::cout << "地图尺寸: " << map.get_width() << "x" << map.get_height() << "x" << map.get_depth() << std::endl;
    print_result("基本属性获取测试", test1_success);

    // 测试 2: 可通行性检查 (初始状态下所有点都应该是可通行的)
    print_separator("测试 2: 可通行性检查 (初始状态)");
    bool test2_success = true;

    // 检查几个点的可通行性
    std::vector<Point3D> test_points = {
        {10, 10, 5},
        {50, 50, 5},
        {90, 90, 5}};

    for (const auto &point : test_points)
    {
        bool is_traversable = map.is_traversable(point);
        std::cout << "点 (" << std::get<0>(point) << ", " << std::get<1>(point) << ", " << std::get<2>(point)
                  << ") 是否可通行: " << (is_traversable ? "是" : "否") << std::endl;
        test2_success &= is_traversable;
    }

    print_result("初始可通行性测试", test2_success);

    // 测试 3: 添加圆柱形禁飞区
    print_separator("测试 3: 添加圆柱形禁飞区");

    // 创建一个空的计划路径映射 (用于冲突检测)
    std::map<std::string, std::vector<GridNode3D>> planned_paths_map;
    std::vector<std::string> conflicting_flight_ids;

    // 计算地图中心的栅格坐标
    int center_grid_x = width / 2;  // 50
    int center_grid_y = height / 2; // 50

    // 将栅格坐标转换为地理坐标
    GeoCoordinate center_geo = converter.grid_to_geographic(center_grid_x, center_grid_y, 0);
    double center_lat = center_geo.lat;
    double center_lon = center_geo.lon;

    // 使用较大的半径确保覆盖足够区域
    double radius_meters = 100.0; // 100米半径
    std::string zone_name = "test_cylinder_zone";
    int buffer_distance_meters = 10;
    int boundary_thickness_grids = 2; // 使用空心圆柱体，边界厚度为2

    std::cout << "禁飞区中心地理坐标: (" << center_lat << ", " << center_lon << ")" << std::endl;
    std::cout << "禁飞区中心栅格坐标: (" << center_grid_x << ", " << center_grid_y << ")" << std::endl;
    std::cout << "禁飞区半径: " << radius_meters << " 米" << std::endl;

    auto result_cylinder = map.add_solid_cylindrical_no_fly_zone(
        center_lat, center_lon, radius_meters,
        zone_name,
        buffer_distance_meters,
        planned_paths_map,
        boundary_thickness_grids);

    bool add_cylinder_success = result_cylinder.first;
    std::vector<std::string> conflicting_ids_cylinder = result_cylinder.second;

    std::cout << "添加圆柱形禁飞区: " << (add_cylinder_success ? "成功" : "失败") << std::endl;

    // 测试 4: 检查禁飞区影响的可通行性
    print_separator("测试 4: 检查禁飞区影响的可通行性");

    // 使用已知的中心点栅格坐标
    Point3D center_point = {center_grid_x, center_grid_y, 5};
    bool center_traversable = map.is_traversable(center_point);

    std::cout << "禁飞区中心点 (" << std::get<0>(center_point) << ", "
              << std::get<1>(center_point) << ", " << std::get<2>(center_point)
              << ") 是否可通行: " << (center_traversable ? "是" : "否") << std::endl;

    // 检查远离中心的点 (应该是可通行的)
    Point3D far_point = {5, 5, 5};
    bool far_traversable = map.is_traversable(far_point);

    std::cout << "远离禁飞区的点 (" << std::get<0>(far_point) << ", "
              << std::get<1>(far_point) << ", " << std::get<2>(far_point)
              << ") 是否可通行: " << (far_traversable ? "是" : "否") << std::endl;

    // 计算圆柱体边界上的点
    // 使用已有的 center_geo 变量
    // 将半径转换为栅格单位
    float radius_grids = converter.convert_radius_meters_to_grids_using_lat_resolution(radius_meters);

    // 计算边界上的点（在圆柱体边界上，而不是中心）
    int edge_x = center_grid_x + static_cast<int>(std::round(radius_grids));
    int edge_y = center_grid_y;

    std::cout << "圆柱体半径（栅格单位）: " << radius_grids << std::endl;
    std::cout << "圆柱体边界点栅格坐标: (" << edge_x << ", " << edge_y << ")" << std::endl;

    Point3D edge_point = {edge_x, edge_y, 5}; // 位于边界上的点
    bool edge_traversable = map.is_traversable(edge_point);

    std::cout << "禁飞区边界上的点 (" << std::get<0>(edge_point) << ", "
              << std::get<1>(edge_point) << ", " << std::get<2>(edge_point)
              << ") 是否可通行: " << (edge_traversable ? "是" : "否") << std::endl;

    // 对于空心圆柱体，中心点应该是可通行的，边界点应该是不可通行的
    bool test4_success = center_traversable && far_traversable && !edge_traversable;

    std::cout << "测试逻辑: 中心点可通行 && 远离点可通行 && 边界点不可通行" << std::endl;
    std::cout << "测试结果: " << center_traversable << " && " << far_traversable << " && " << !edge_traversable << std::endl;
    print_result("禁飞区可通行性测试", test4_success);

    // 测试 5: 添加多边形禁飞区
    print_separator("测试 5: 添加多边形禁飞区");

    // 在地图的另一个区域创建一个多边形
    int second_center_x = width / 4;  // 25
    int second_center_y = height / 4; // 25

    // 创建多边形顶点（围绕中心点的矩形）
    std::vector<GeoCoordinate> polygon_vertices;

    // 将栅格坐标转换为地理坐标
    GeoCoordinate top_left = converter.grid_to_geographic(second_center_x - 10, second_center_y - 10, 0);
    GeoCoordinate top_right = converter.grid_to_geographic(second_center_x + 10, second_center_y - 10, 0);
    GeoCoordinate bottom_right = converter.grid_to_geographic(second_center_x + 10, second_center_y + 10, 0);
    GeoCoordinate bottom_left = converter.grid_to_geographic(second_center_x - 10, second_center_y + 10, 0);

    polygon_vertices.push_back(top_left);
    polygon_vertices.push_back(top_right);
    polygon_vertices.push_back(bottom_right);
    polygon_vertices.push_back(bottom_left);

    std::string poly_zone_name = "test_polygon_zone";
    double offset_meters = 5.0;      // 几何外扩距离
    int poly_boundary_thickness = 1; // 边界厚度

    std::cout << "多边形禁飞区中心栅格坐标: (" << second_center_x << ", " << second_center_y << ")" << std::endl;
    std::cout << "多边形顶点数量: " << polygon_vertices.size() << std::endl;
    std::cout << "几何外扩距离: " << offset_meters << " 米" << std::endl;

    auto result_polygon = map.add_hollow_polygonal_no_fly_zone(
        polygon_vertices,
        poly_zone_name,
        offset_meters,
        poly_boundary_thickness,
        planned_paths_map);

    bool add_polygon_success = result_polygon.first;
    std::vector<std::string> conflicting_ids_polygon = result_polygon.second;

    std::cout << "添加多边形禁飞区: " << (add_polygon_success ? "成功" : "失败") << std::endl;

    // 检查多边形禁飞区中心点的可通行性（应该是可通行的，因为是空心多边形）
    Point3D second_center_point = {second_center_x, second_center_y, 5};
    bool second_center_traversable = map.is_traversable(second_center_point);

    std::cout << "多边形禁飞区中心点 (" << std::get<0>(second_center_point) << ", "
              << std::get<1>(second_center_point) << ", " << std::get<2>(second_center_point)
              << ") 是否可通行: " << (second_center_traversable ? "是" : "否") << std::endl;

    // 检查多边形禁飞区边界点的可通行性（应该是不可通行的）
    Point3D polygon_edge_point = {second_center_x - 10, second_center_y, 5}; // 位于多边形边界上
    bool polygon_edge_traversable = map.is_traversable(polygon_edge_point);

    std::cout << "多边形禁飞区边界点 (" << std::get<0>(polygon_edge_point) << ", "
              << std::get<1>(polygon_edge_point) << ", " << std::get<2>(polygon_edge_point)
              << ") 是否可通行: " << (polygon_edge_traversable ? "是" : "否") << std::endl;

    // 检查多边形禁飞区内部点（但不是中心点）的可通行性
    Point3D polygon_inner_point = {second_center_x - 5, second_center_y - 5, 5}; // 位于多边形内部
    bool polygon_inner_traversable = map.is_traversable(polygon_inner_point);

    std::cout << "多边形禁飞区内部点 (" << std::get<0>(polygon_inner_point) << ", "
              << std::get<1>(polygon_inner_point) << ", " << std::get<2>(polygon_inner_point)
              << ") 是否可通行: " << (polygon_inner_traversable ? "是" : "否") << std::endl;

    // 测试 6: 删除禁飞区
    print_separator("测试 6: 删除禁飞区");

    // 删除第一个圆柱形禁飞区
    bool remove_cylinder_success = map.remove_no_fly_zone(zone_name);
    std::cout << "删除第一个圆柱形禁飞区: " << (remove_cylinder_success ? "成功" : "失败") << std::endl;

    // 检查第一个禁飞区中心点的可通行性 (删除后应该是可通行的)
    bool center_traversable_after_remove = map.is_traversable(center_point);
    std::cout << "删除后，第一个禁飞区中心点 (" << std::get<0>(center_point) << ", "
              << std::get<1>(center_point) << ", " << std::get<2>(center_point)
              << ") 是否可通行: " << (center_traversable_after_remove ? "是" : "否") << std::endl;

    // 检查第一个禁飞区边界点的可通行性 (删除后应该是可通行的)
    bool edge_traversable_after_remove = map.is_traversable(edge_point);
    std::cout << "删除后，第一个禁飞区边界点 (" << std::get<0>(edge_point) << ", "
              << std::get<1>(edge_point) << ", " << std::get<2>(edge_point)
              << ") 是否可通行: " << (edge_traversable_after_remove ? "是" : "否") << std::endl;

    // 删除多边形禁飞区
    bool remove_polygon_success = map.remove_no_fly_zone(poly_zone_name);
    std::cout << "删除多边形禁飞区: " << (remove_polygon_success ? "成功" : "失败") << std::endl;

    // 检查多边形禁飞区中心点的可通行性 (删除后应该是可通行的)
    bool second_center_traversable_after_remove = map.is_traversable(second_center_point);
    std::cout << "删除后，多边形禁飞区中心点 (" << std::get<0>(second_center_point) << ", "
              << std::get<1>(second_center_point) << ", " << std::get<2>(second_center_point)
              << ") 是否可通行: " << (second_center_traversable_after_remove ? "是" : "否") << std::endl;

    // 检查多边形禁飞区边界点的可通行性 (删除后应该是可通行的)
    bool polygon_edge_traversable_after_remove = map.is_traversable(polygon_edge_point);
    std::cout << "删除后，多边形禁飞区边界点 (" << std::get<0>(polygon_edge_point) << ", "
              << std::get<1>(polygon_edge_point) << ", " << std::get<2>(polygon_edge_point)
              << ") 是否可通行: " << (polygon_edge_traversable_after_remove ? "是" : "否") << std::endl;

    // 检查多边形禁飞区内部点的可通行性 (删除后应该是可通行的)
    bool polygon_inner_traversable_after_remove = map.is_traversable(polygon_inner_point);
    std::cout << "删除后，多边形禁飞区内部点 (" << std::get<0>(polygon_inner_point) << ", "
              << std::get<1>(polygon_inner_point) << ", " << std::get<2>(polygon_inner_point)
              << ") 是否可通行: " << (polygon_inner_traversable_after_remove ? "是" : "否") << std::endl;

    // 测试 7: 删除不存在的禁飞区
    print_separator("测试 7: 删除不存在的禁飞区");

    bool remove_nonexistent_success = map.remove_no_fly_zone("nonexistent_zone");
    std::cout << "删除不存在的禁飞区: " << (remove_nonexistent_success ? "成功" : "失败") << std::endl;
    print_result("删除不存在禁飞区测试", remove_nonexistent_success);

    // 总结测试结果
    print_separator("测试总结");

    // 检查各种测试结果
    bool cylinder_edge_test = !edge_traversable;          // 圆柱体边界点应该不可通行
    bool cylinder_center_test = center_traversable;       // 圆柱体中心点应该可通行
    bool polygon_edge_test = !polygon_edge_traversable;   // 多边形边界点应该不可通行
    bool polygon_center_test = second_center_traversable; // 多边形中心点应该可通行
    bool polygon_inner_test = polygon_inner_traversable;  // 多边形内部点应该可通行

    // 删除后的测试
    bool edge_remove_test = edge_traversable_after_remove;                   // 删除后边界点应该可通行
    bool polygon_edge_remove_test = polygon_edge_traversable_after_remove;   // 删除后多边形边界点应该可通行
    bool polygon_inner_remove_test = polygon_inner_traversable_after_remove; // 删除后多边形内部点应该可通行

    bool all_tests_passed = test1_success && test2_success && test4_success &&
                            add_cylinder_success && add_polygon_success &&
                            remove_cylinder_success && remove_polygon_success &&
                            remove_nonexistent_success &&
                            cylinder_edge_test && cylinder_center_test &&
                            polygon_edge_test && polygon_center_test && polygon_inner_test &&
                            edge_remove_test && polygon_edge_remove_test && polygon_inner_remove_test;

    std::cout << "所有测试: " << (all_tests_passed ? "通过" : "部分失败") << std::endl;

    // 详细测试结果
    std::cout << "\n详细测试结果:\n";
    std::cout << "1. 基本属性获取测试: " << (test1_success ? "通过" : "失败") << std::endl;
    std::cout << "2. 初始可通行性测试: " << (test2_success ? "通过" : "失败") << std::endl;
    std::cout << "3. 添加圆柱形禁飞区: " << (add_cylinder_success ? "通过" : "失败") << std::endl;
    std::cout << "4. 圆柱体边界点不可通行测试: " << (cylinder_edge_test ? "通过" : "失败") << std::endl;
    std::cout << "5. 圆柱体中心点可通行测试: " << (cylinder_center_test ? "通过" : "失败") << std::endl;
    std::cout << "6. 添加多边形禁飞区: " << (add_polygon_success ? "通过" : "失败") << std::endl;
    std::cout << "7. 多边形边界点不可通行测试: " << (polygon_edge_test ? "通过" : "失败") << std::endl;
    std::cout << "8. 多边形中心点可通行测试: " << (polygon_center_test ? "通过" : "失败") << std::endl;
    std::cout << "9. 多边形内部点可通行测试: " << (polygon_inner_test ? "通过" : "失败") << std::endl;
    std::cout << "10. 删除圆柱形禁飞区: " << (remove_cylinder_success ? "通过" : "失败") << std::endl;
    std::cout << "11. 删除多边形禁飞区: " << (remove_polygon_success ? "通过" : "失败") << std::endl;
    std::cout << "12. 删除不存在的禁飞区: " << (remove_nonexistent_success ? "通过" : "失败") << std::endl;
    std::cout << "13. 删除后圆柱体边界点可通行: " << (edge_remove_test ? "通过" : "失败") << std::endl;
    std::cout << "14. 删除后多边形边界点可通行: " << (polygon_edge_remove_test ? "通过" : "失败") << std::endl;
    std::cout << "15. 删除后多边形内部点可通行: " << (polygon_inner_remove_test ? "通过" : "失败") << std::endl;
}

static void test_occupancy_map()
{
    print_separator("OccupancyMap 功能测试");

    // OccupancyMap 测试参数
    int occ_map_height = 10;
    int occ_map_width = 10;
    int occ_map_depth = 5;
    int time_buffer_seconds = 4;   // 例如，每个点占用4秒的窗口
    int safety_envelope_grids = 0; // 安全包络，测试时可以设为0或一个较小的值

    OccupancyMap occ_map(occ_map_height, occ_map_width, occ_map_depth, time_buffer_seconds, safety_envelope_grids);
    std::cout << "创建了 " << occ_map_width << "x" << occ_map_height << "x" << occ_map_depth
              << " 的 OccupancyMap 实例，时间窗口: " << time_buffer_seconds << "s, 安全包络: " << safety_envelope_grids << "\n";

    // 测试 OM1: 基本初始化和空状态检查
    print_separator("OM 测试 1: 初始化和空状态");
    bool om_test1_success = true;
    Point3D test_om_point1 = {1, 1, 1};
    om_test1_success &= occ_map.get_occupancies_at_point(test_om_point1).empty();
    om_test1_success &= occ_map.get_agent_occupied_points("flight_non_existent").empty();
    om_test1_success &= !occ_map.check_collision(test_om_point1, 100LL);
    print_result("OM 初始化和空状态测试", om_test1_success);

    // 测试 OM2: add_path 功能
    print_separator("OM 测试 2: add_path 功能");
    bool om_test2_success = true;
    std::string flight1_id = "FL001";
    std::vector<GridNode3D> path1;
    // 路径点: (1,1,1) at t=100, (1,2,1) at t=102, (1,3,1) at t=104
    path1.emplace_back(1.0f, 1.0f, 1.0f, 100LL);
    path1.emplace_back(1.0f, 2.0f, 1.0f, 102LL);
    path1.emplace_back(1.0f, 3.0f, 1.0f, 104LL);
    occ_map.add_path(flight1_id, path1);

    auto occupied_by_fl001 = occ_map.get_agent_occupied_points(flight1_id);
    om_test2_success &= (occupied_by_fl001.count({1, 1, 1}) == 1);
    om_test2_success &= (occupied_by_fl001.count({1, 2, 1}) == 1);
    om_test2_success &= (occupied_by_fl001.count({1, 3, 1}) == 1);
    om_test2_success &= (occupied_by_fl001.size() == 3);

    // 检查 (1,1,1) at t=100, buffer=4. half_buffer=2. Interval [98, 102]
    auto intervals_p1 = occ_map.get_occupancies_at_point({1, 1, 1});
    om_test2_success &= (intervals_p1.size() == 1);
    if (!intervals_p1.empty())
    {
        om_test2_success &= (intervals_p1[0].start_time_ == 98LL && intervals_p1[0].end_time_ == 102LL && intervals_p1[0].flight_id_ == flight1_id);
    }
    // 检查 (1,2,1) at t=102, buffer=4. Interval [100, 104]
    auto intervals_p2 = occ_map.get_occupancies_at_point({1, 2, 1});
    om_test2_success &= (intervals_p2.size() == 1);
    if (!intervals_p2.empty())
    {
        om_test2_success &= (intervals_p2[0].start_time_ == 100LL && intervals_p2[0].end_time_ == 104LL && intervals_p2[0].flight_id_ == flight1_id);
    }
    print_result("OM add_path 测试", om_test2_success);

    // 测试 OM3: check_collision 功能
    print_separator("OM 测试 3: check_collision 功能");
    bool om_test3_success = true;
    // Point (1,1,1) is occupied [98, 102] by FL001
    om_test3_success &= occ_map.check_collision({1, 1, 1}, 98LL);
    om_test3_success &= occ_map.check_collision({1, 1, 1}, 100LL);
    om_test3_success &= occ_map.check_collision({1, 1, 1}, 102LL);
    om_test3_success &= !occ_map.check_collision({1, 1, 1}, 97LL);  // Before interval
    om_test3_success &= !occ_map.check_collision({1, 1, 1}, 103LL); // After interval
    // Point (1,2,1) is occupied [100, 104] by FL001
    om_test3_success &= occ_map.check_collision({1, 2, 1}, 102LL);
    // Point not on path
    om_test3_success &= !occ_map.check_collision({5, 5, 3}, 100LL);
    print_result("OM check_collision 测试", om_test3_success);

    // 测试 OM4: 时间区间合并 (同一航班ID)
    // FL001 already has (1,2,1) at t=102, interval [100,104]
    // Add another path for FL001 that overlaps this
    print_separator("OM 测试 4: 时间区间合并 (同一航班ID)");
    bool om_test4_success = true;
    std::vector<GridNode3D> path1_overlap;
    // Path point: (1,2,1) at t=103. Interval [101, 105]
    // This should merge with existing [100,104] to become [100,105]
    path1_overlap.emplace_back(1.0f, 2.0f, 1.0f, 103LL);
    occ_map.add_path(flight1_id, path1_overlap);

    auto intervals_p2_merged = occ_map.get_occupancies_at_point({1, 2, 1});
    om_test4_success &= (intervals_p2_merged.size() == 1); // Should still be one interval after merge
    if (!intervals_p2_merged.empty())
    {
        om_test4_success &= (intervals_p2_merged[0].start_time_ == 100LL && intervals_p2_merged[0].end_time_ == 105LL);
        std::cout << "Merged interval for (1,2,1): [" << intervals_p2_merged[0].start_time_ << ", " << intervals_p2_merged[0].end_time_ << "]" << std::endl;
    }
    else
    {
        std::cout << "No intervals found for (1,2,1) after supposed merge." << std::endl;
    }
    print_result("OM 时间区间合并测试", om_test4_success);

    // 测试 OM5: 不同航班ID在同一点的占用 (时间不重叠)
    print_separator("OM 测试 5: 不同航班ID, 同一点, 时间不重叠");
    bool om_test5_success = true;
    std::string flight2_id = "FL002";
    std::vector<GridNode3D> path2;
    // Path point for FL002: (1,3,1) at t=110. Interval [108, 112]
    // FL001 has (1,3,1) at t=104. Interval [102, 106]
    path2.emplace_back(1.0f, 3.0f, 1.0f, 110LL);
    occ_map.add_path(flight2_id, path2);

    auto intervals_p3_multi_flight = occ_map.get_occupancies_at_point({1, 3, 1});
    om_test5_success &= (intervals_p3_multi_flight.size() == 2); // Should be two separate intervals
    if (intervals_p3_multi_flight.size() == 2)
    {
        // Sort by flight_id to ensure consistent order for checking
        std::sort(intervals_p3_multi_flight.begin(), intervals_p3_multi_flight.end(),
                  [](const TimeInterval &a, const TimeInterval &b)
                  { return a.flight_id_ < b.flight_id_; });
        om_test5_success &= (intervals_p3_multi_flight[0].flight_id_ == flight1_id && intervals_p3_multi_flight[0].start_time_ == 102LL && intervals_p3_multi_flight[0].end_time_ == 106LL);
        om_test5_success &= (intervals_p3_multi_flight[1].flight_id_ == flight2_id && intervals_p3_multi_flight[1].start_time_ == 108LL && intervals_p3_multi_flight[1].end_time_ == 112LL);
    }
    om_test5_success &= occ_map.check_collision({1, 3, 1}, 105LL);  // FL001
    om_test5_success &= occ_map.check_collision({1, 3, 1}, 110LL);  // FL002
    om_test5_success &= !occ_map.check_collision({1, 3, 1}, 107LL); // Gap
    print_result("OM 不同航班ID, 不同时间测试", om_test5_success);

    // 测试 OM6: 不同航班ID在同一点的占用 (时间重叠)
    print_separator("OM 测试 6: 不同航班ID, 同一点, 时间重叠");
    bool om_test6_success = true;
    std::string flight3_id = "FL003";
    std::vector<GridNode3D> path3;
    // Path point for FL003: (1,1,1) at t=101. Interval [99, 103]
    // FL001 has (1,1,1) at t=100. Interval [98, 102]
    // These should overlap but remain separate as they are different flights
    path3.emplace_back(1.0f, 1.0f, 1.0f, 101LL);
    occ_map.add_path(flight3_id, path3);
    auto intervals_p1_overlap_diff_flight = occ_map.get_occupancies_at_point({1, 1, 1});
    om_test6_success &= (intervals_p1_overlap_diff_flight.size() == 2);
    if (intervals_p1_overlap_diff_flight.size() == 2)
    {
        std::sort(intervals_p1_overlap_diff_flight.begin(), intervals_p1_overlap_diff_flight.end(),
                  [](const TimeInterval &a, const TimeInterval &b)
                  { return a.flight_id_ < b.flight_id_; });
        om_test6_success &= (intervals_p1_overlap_diff_flight[0].flight_id_ == flight1_id && intervals_p1_overlap_diff_flight[0].start_time_ == 98LL && intervals_p1_overlap_diff_flight[0].end_time_ == 102LL);
        om_test6_success &= (intervals_p1_overlap_diff_flight[1].flight_id_ == flight3_id && intervals_p1_overlap_diff_flight[1].start_time_ == 99LL && intervals_p1_overlap_diff_flight[1].end_time_ == 103LL);
    }
    om_test6_success &= occ_map.check_collision({1, 1, 1}, 98LL);  // FL001 only
    om_test6_success &= occ_map.check_collision({1, 1, 1}, 99LL);  // Both
    om_test6_success &= occ_map.check_collision({1, 1, 1}, 102LL); // Both
    om_test6_success &= occ_map.check_collision({1, 1, 1}, 103LL); // FL003 only
    print_result("OM 不同航班ID, 时间重叠测试", om_test6_success);

    // 测试 OM7: remove_agent 功能
    print_separator("OM 测试 7: remove_agent 功能");
    bool om_test7_success = true;
    occ_map.remove_agent(flight1_id);
    om_test7_success &= occ_map.get_agent_occupied_points(flight1_id).empty();
    // Point (1,1,1) was occupied by FL001 [98,102] and FL003 [99,103]. After removing FL001, only FL003 should remain.
    auto intervals_p1_after_remove = occ_map.get_occupancies_at_point({1, 1, 1});
    om_test7_success &= (intervals_p1_after_remove.size() == 1);
    if (!intervals_p1_after_remove.empty())
    {
        om_test7_success &= (intervals_p1_after_remove[0].flight_id_ == flight3_id);
    }
    om_test7_success &= !occ_map.check_collision({1, 1, 1}, 98LL); // Was FL001, now free
    om_test7_success &= occ_map.check_collision({1, 1, 1}, 99LL);  // Still FL003
    // Point (1,2,1) was only FL001 [100,105]. Should be free.
    om_test7_success &= !occ_map.check_collision({1, 2, 1}, 100LL);
    om_test7_success &= occ_map.get_occupancies_at_point({1, 2, 1}).empty();
    // Point (1,3,1) was FL001 [102,106] and FL002 [108,112]. FL002 should remain.
    auto intervals_p3_after_remove = occ_map.get_occupancies_at_point({1, 3, 1});
    om_test7_success &= (intervals_p3_after_remove.size() == 1);
    if (!intervals_p3_after_remove.empty())
    {
        om_test7_success &= (intervals_p3_after_remove[0].flight_id_ == flight2_id);
    }
    print_result("OM remove_agent 测试", om_test7_success);

    // 测试 OM8: 边界和特殊情况
    print_separator("OM 测试 8: 边界和特殊情况");
    bool om_test8_success = true;
    std::vector<GridNode3D> empty_path;
    occ_map.add_path("FL_EMPTY", empty_path); // Should do nothing, not crash
    om_test8_success &= occ_map.get_agent_occupied_points("FL_EMPTY").empty();

    occ_map.remove_agent("FL_NON_EXISTENT"); // Should do nothing, not crash
    // No direct check for success other than not crashing, could add more state checks if desired
    print_result("OM 边界情况测试", om_test8_success);
}

// JPS类测试函数
static void test_jps()
{
    print_separator("JPS 跳点搜索算法测试");

    // 创建 GridConverter 实例
    GridCellSize cell_size = {0.0001, 0.0001, 1.0}; // 约 11m x 11m 的栅格
    GeoCoordinate min_coords = {30.0, 120.0, 0.0};  // 地图左下角坐标
    GridConverter converter(cell_size, min_coords);

    // 创建 Map3D 实例
    int height = 10; // 栅格高度 (y 方向)
    int width = 10;  // 栅格宽度 (x 方向)
    int depth = 10;  // 栅格深度 (z 方向)
    Map3D map(height, width, depth, converter);

    // 默认情况下，所有单元格都是可通行的
    std::cout << "地图初始化完成，默认所有单元格都是可通行的。" << std::endl;

    // 检查几个点的可通行性，确认地图初始化正确
    std::vector<Point3D> test_points = {
        {1, 1, 5},
        {5, 5, 5},
        {9, 9, 5}};

    for (const auto &point : test_points)
    {
        bool is_traversable = map.is_traversable(point);
        std::cout << "点 (" << std::get<0>(point) << ", " << std::get<1>(point) << ", " << std::get<2>(point)
                  << ") 是否可通行: " << (is_traversable ? "是" : "否") << std::endl;
    }

    // 创建 OccupancyMap 实例
    int time_buffer_seconds = 600;    // 每个点占用10分钟的时间窗口
    int safety_envelope_jps_test = 3; // JPS测试中的安全包络
    OccupancyMap occupancy_map(height, width, depth, time_buffer_seconds, safety_envelope_jps_test);

    // 创建 JPS 实例 - 使用更小的跳跃步长和更低的启发式权重以提高成功率
    double takeoff_speed = 0.2;    // 起飞速度
    double cruise_speed = 0.7;     // 巡航速度
    double landing_speed = 0.2;    // 降落速度
    int max_steps = 100;           // 最大搜索步数
    bool need_smooth = true;       // 需要平滑处理
    double smoothness = 3.0;       // 平滑程度
    double heuristic_weight = 3.0; // 启发式函数权重（降低以更准确地找到路径）
    int jump_step_size = 3;        // 跳跃步长（降低以提高成功率）

    JPS jps(&map, &occupancy_map, takeoff_speed, cruise_speed, landing_speed,
            max_steps, need_smooth, smoothness, heuristic_weight, jump_step_size);

    std::cout << "创建了 JPS 实例，地图尺寸: " << width << "x" << height << "x" << depth << std::endl;
    std::cout << "起飞速度: " << takeoff_speed << ", 巡航速度: " << cruise_speed
              << ", 降落速度: " << landing_speed << std::endl;
    std::cout << "最大搜索步数: " << max_steps << ", 平滑程度: " << smoothness
              << ", 启发式权重: " << heuristic_weight << ", 跳跃步长: " << jump_step_size << std::endl;

    // 测试 1: 简单路径规划（无障碍物）
    print_separator("测试 1: 简单路径规划（无障碍物）");

    // 创建起点和终点 - 确保起点和终点的高度符合最小飞行高度要求
    int min_height = 5;                  // 最小飞行高度
    GridNode3D start_node(9, 2, 0, 0);   // 起点坐标(10,10,5)，时间戳0
    GridNode3D goal_node(1, 9, 0, 0);    // 终点坐标(90,90,5)，时间戳0
    std::string agent_id = "TEST_AGENT"; // 飞行器ID
    long long path_start_time = 0;       // 路径开始时间

    std::cout << "起点: (" << start_node.x << ", " << start_node.y << ", " << start_node.z << ")" << std::endl;
    std::cout << "终点: (" << goal_node.x << ", " << goal_node.y << ", " << goal_node.z << ")" << std::endl;
    std::cout << "最小飞行高度: " << min_height << std::endl;

    // 执行路径规划
    auto [complete_path, turn_path, error_msg] = jps.find_path(
        start_node, goal_node, min_height, agent_id, path_start_time);

    // 检查结果
    bool test1_success = complete_path.has_value() && !error_msg.has_value();
    std::cout << "路径规划结果: " << (test1_success ? "成功" : "失败") << std::endl;

    if (error_msg.has_value())
    {
        std::cout << "错误信息: " << error_msg.value() << std::endl;
    }

    if (complete_path.has_value())
    {
        std::cout << "路径点数量: " << complete_path.value().size() << std::endl;

        // 打印路径的起点、中点和终点
        if (!complete_path.value().empty())
        {
            auto &path = complete_path.value();
            std::cout << "路径起点: (" << path.front().x << ", " << path.front().y << ", " << path.front().z
                      << "), 时间: " << path.front().t << std::endl;

            if (path.size() > 2)
            {
                size_t mid_idx = path.size() / 2;
                std::cout << "路径中点: (" << path[mid_idx].x << ", " << path[mid_idx].y << ", " << path[mid_idx].z
                          << "), 时间: " << path[mid_idx].t << std::endl;
            }

            std::cout << "路径终点: (" << path.back().x << ", " << path.back().y << ", " << path.back().z
                      << "), 时间: " << path.back().t << std::endl;
        }
    }

    if (turn_path.has_value())
    {
        std::cout << "转弯点数量: " << turn_path.value().size() << std::endl;
    }

    print_result("简单路径规划测试", test1_success);

    // 测试 2: 添加障碍物后的路径规划
    print_separator("测试 2: 添加障碍物后的路径规划");

    // 添加一个圆柱形障碍物
    // int center_x = width / 2;   // 5
    // int center_y = height / 2;  // 5
    // double radius_meters = 2.0; // 2米半径
    // std::string zone_name = "test_obstacle";
    // int buffer_distance_meters = 1;
    std::map<std::string, std::vector<GridNode3D>> planned_paths_map;
    std::vector<std::string> conflicting_flight_ids;
    // int boundary_thickness_grids = 6;

    // // 将栅格坐标转换为地理坐标
    // GeoCoordinate center_geo = converter.grid_to_geographic(center_x, center_y, 0);
    // double center_lat = center_geo.lat;
    // double center_lon = center_geo.lon;

    // std::cout << "添加圆柱形障碍物，中心点: (" << center_x << ", " << center_y << "), 半径: "
    //           << radius_meters << "米" << std::endl;

    // bool add_obstacle_success = map.add_solid_cylindrical_no_fly_zone(
    //     center_lat, center_lon, radius_meters,
    //     zone_name,
    //     buffer_distance_meters,
    //     planned_paths_map,
    //     boundary_thickness_grids,
    //     conflicting_flight_ids);

    // std::cout << "添加障碍物: " << (add_obstacle_success ? "成功" : "失败") << std::endl;

    std::string flight1_id = "FL001";
    std::vector<GridNode3D> path1;
    path1.emplace_back(9.0f, 4.0f, 5.0f, 0LL);
    path1.emplace_back(7.0f, 4.0f, 5.0f, 1LL);
    path1.emplace_back(5.0f, 4.0f, 5.0f, 2LL);
    occupancy_map.add_path(flight1_id, path1);

    map.add_solid_cylindrical_no_fly_zone_grid(5, 4, 3, "no_fly_zone1", planned_paths_map, 3);

    // 更新地图内部障碍物
    map.update_internal_obstacles();

    // 执行路径规划（绕过障碍物）
    auto [path_with_obstacle, turn_path_with_obstacle, error_msg_with_obstacle] =
        jps.find_path(start_node, goal_node, min_height, agent_id, path_start_time);

    // 检查结果
    bool test2_success = path_with_obstacle.has_value() && !error_msg_with_obstacle.has_value();
    std::cout << "有障碍物的路径规划结果: " << (test2_success ? "成功" : "失败") << std::endl;

    if (error_msg_with_obstacle.has_value())
    {
        std::cout << "错误信息: " << error_msg_with_obstacle.value() << std::endl;
    }

    if (path_with_obstacle.has_value())
    {
        std::cout << "路径点数量: " << path_with_obstacle.value().size() << std::endl;

        // 打印路径的起点、中点和终点
        if (!path_with_obstacle.value().empty())
        {
            auto &path = path_with_obstacle.value();
            std::cout << "路径起点: (" << path.front().x << ", " << path.front().y << ", " << path.front().z
                      << "), 时间: " << path.front().t << std::endl;

            if (path.size() > 2)
            {
                size_t mid_idx = path.size() / 2;
                std::cout << "路径中点: (" << path[mid_idx].x << ", " << path[mid_idx].y << ", " << path[mid_idx].z
                          << "), 时间: " << path[mid_idx].t << std::endl;
            }

            std::cout << "路径终点: (" << path.back().x << ", " << path.back().y << ", " << path.back().z
                      << "), 时间: " << path.back().t << std::endl;
        }
    }

    if (turn_path_with_obstacle.has_value())
    {
        std::cout << "转弯点数量: " << turn_path_with_obstacle.value().size() << std::endl;
    }

    print_result("有障碍物的路径规划测试", test2_success);

    // 测试 3: 不同高度的路径规划
    print_separator("测试 3: 不同高度的路径规划");

    // 创建不同高度的起点和终点 - 确保起点高度符合最小飞行高度要求
    GridNode3D start_node_diff_height(1, 1, 3, 0); // 起点坐标(10,10,5)，时间戳0
    GridNode3D goal_node_diff_height(9, 9, 2, 0);  // 终点坐标(90,90,8)，时间戳0

    std::cout << "起点: (" << start_node_diff_height.x << ", " << start_node_diff_height.y
              << ", " << start_node_diff_height.z << ")" << std::endl;
    std::cout << "终点: (" << goal_node_diff_height.x << ", " << goal_node_diff_height.y
              << ", " << goal_node_diff_height.z << ")" << std::endl;

    // 执行路径规划
    auto [path_diff_height, turn_path_diff_height, error_msg_diff_height] =
        jps.find_path(start_node_diff_height, goal_node_diff_height, min_height, agent_id, path_start_time);

    // 检查结果
    bool test3_success = path_diff_height.has_value() && !error_msg_diff_height.has_value();
    std::cout << "不同高度的路径规划结果: " << (test3_success ? "成功" : "失败") << std::endl;

    if (error_msg_diff_height.has_value())
    {
        std::cout << "错误信息: " << error_msg_diff_height.value() << std::endl;
    }

    if (path_diff_height.has_value())
    {
        std::cout << "路径点数量: " << path_diff_height.value().size() << std::endl;

        // 打印路径的起点、中点和终点
        if (!path_diff_height.value().empty())
        {
            auto &path = path_diff_height.value();
            std::cout << "路径起点: (" << path.front().x << ", " << path.front().y << ", " << path.front().z
                      << "), 时间: " << path.front().t << std::endl;

            if (path.size() > 2)
            {
                size_t mid_idx = path.size() / 2;
                std::cout << "路径中点: (" << path[mid_idx].x << ", " << path[mid_idx].y << ", " << path[mid_idx].z
                          << "), 时间: " << path[mid_idx].t << std::endl;
            }

            std::cout << "路径终点: (" << path.back().x << ", " << path.back().y << ", " << path.back().z
                      << "), 时间: " << path.back().t << std::endl;
        }
    }

    print_result("不同高度的路径规划测试", test3_success);

    // 测试总结
    print_separator("JPS测试总结");
    bool all_tests_passed = test1_success && test2_success && test3_success;
    std::cout << "所有JPS测试: " << (all_tests_passed ? "通过" : "部分失败") << std::endl;

    // 详细测试结果
    std::cout << "\n详细测试结果:\n";
    std::cout << "1. 简单路径规划测试: " << (test1_success ? "通过" : "失败") << std::endl;
    std::cout << "2. 有障碍物的路径规划测试: " << (test2_success ? "通过" : "失败") << std::endl;
    std::cout << "3. 不同高度的路径规划测试: " << (test3_success ? "通过" : "失败") << std::endl;
}

int main()
{
    // test_map3d();
    // test_occupancy_map();
    test_jps();
    return 0;
}
