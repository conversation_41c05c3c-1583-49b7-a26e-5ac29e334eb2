import math
from collections import defaultdict
from typing import Dict, List, Set, Tuple, Optional

from pathfinding_cpp import OccupancyMap


def item_in_list_3d(l, idx):
    """获取列表中指定索引的项，如果超出范围则返回最后一项的坐标"""
    try:
        return l[idx]
    except IndexError:
        return l[-1]


class CTNode3D:
    """CBS约束树节点"""

    def __init__(
        self,
        constraints,
        solution,
        cost,
        parent=None,
        entry=0,
        map_size=(100, 100, 100),
    ):
        """初始化CT节点

        Args:
            constraints: 约束集合，每个约束为 (agent_id, y, x, z, t)
            solution: 解决方案字典，{agent_id: path}
            cost: 总代价
            parent: 父节点
            entry: 用于在OPEN列表中打破平局
            map_size: 地图大小 (height, width, depth)
        """
        self.constraints = constraints
        self.solution = solution
        self.cost = cost
        self.parent = parent
        self.entry = entry
        self.n_conflicts = None
        self.map_size = map_size
        self._occupancy_map = None  # 延迟初始化占用图

    def __eq__(self, other):
        return self.solution == other.solution

    def __lt__(self, other):
        # 优先级：冲突数 > 代价 > 入队顺序
        self_n = self.count_n_of_conflicts()
        other_n = other.count_n_of_conflicts()
        if self_n != other_n:
            return self_n < other_n
        if self.cost != other.cost:
            return self.cost < other.cost
        return self.entry < other.entry

    @property
    def occupancy_map(self):
        """延迟初始化并缓存占用图"""
        if self._occupancy_map is None:
            self._occupancy_map = OccupancyMap(self.map_size, time_buffer=0)
            # 添加所有路径到占用图
            for agent_id, path in self.solution.items():
                self._occupancy_map.add_path(path, agent_id)
        return self._occupancy_map

    def validate_conflicts(self, use_pc=False):
        """验证路径冲突

        Args:
            use_pc: 是否使用路径约束（暂未实现）

        Returns:
            冲突列表
        """
        conflicts = []
        # 遍历每个智能体的路径
        for agent_id, path in self.solution.items():
            for node in path:
                # 检查是否有冲突
                has_collision, colliding_agent = self.occupancy_map.check_collision(
                    (node.y, node.x, node.z), node.t
                )
                if has_collision and colliding_agent != agent_id:
                    conflicts.append(
                        (
                            "v",
                            (agent_id, colliding_agent),
                            (node.y, node.x, node.z, node.t),
                        )
                    )
                    if not use_pc:  # 如果不使用路径约束，找到第一个冲突就返回
                        return conflicts
        return conflicts

    def count_n_of_conflicts(self):
        """计算冲突数量"""
        if self.n_conflicts is None:
            self.n_conflicts = len(self.validate_conflicts(use_pc=True))
        return self.n_conflicts

    def get_all_constraints(self):
        """获取从根节点到当前节点的所有约束"""
        all_constraints = set()
        node = self
        while node:
            all_constraints.update(node.constraints)
            node = node.parent
        return all_constraints
