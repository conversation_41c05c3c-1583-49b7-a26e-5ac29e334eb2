#include "cpp_implementation/include/Map3D.h"
#include "cpp_implementation/include/GridConverter.h"
#include <iostream>
#include <cassert>

int main() {
    std::cout << "测试边界厚度向内扩展功能..." << std::endl;
    
    // 创建一个简单的网格转换器
    GridConverter converter(39.9, 116.4, 100.0, 100.0, 20, 20);
    
    // 创建一个20x20x5的地图
    Map3D map(20, 20, 5, converter);
    
    std::cout << "地图创建成功" << std::endl;
    
    // 测试1：创建一个简单的正方形多边形禁飞区
    std::cout << "\n--- 测试1：正方形多边形禁飞区 ---" << std::endl;
    std::map<std::string, std::vector<GridNode3D>> empty_paths;
    
    // 定义一个5x5的正方形，中心在(10, 10)
    std::vector<std::pair<float, float>> square_vertices = {
        {8.0f, 8.0f},   // 左下角
        {12.0f, 8.0f},  // 右下角
        {12.0f, 12.0f}, // 右上角
        {8.0f, 12.0f}   // 左上角
    };
    
    // 测试不同的边界厚度
    for (int thickness = 1; thickness <= 3; ++thickness) {
        std::string zone_name = "test_square_thickness_" + std::to_string(thickness);
        
        auto result = map.add_hollow_polygonal_no_fly_zone_grid(
            square_vertices,
            zone_name,
            0.0f,        // 无偏移
            thickness,   // 边界厚度
            empty_paths
        );
        
        if (result.first) {
            std::cout << "✓ 边界厚度 " << thickness << " 的正方形禁飞区添加成功" << std::endl;
            
            // 测试几何检查功能
            std::string check_result;
            
            // 测试中心点（应该在禁飞区内，因为是中空的）
            check_result = map.is_point_inside_any_nfz_2d({10, 10});
            std::cout << "  中心点(10,10): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
            
            // 测试边界点（应该在禁飞区内）
            check_result = map.is_point_inside_any_nfz_2d({8, 10});
            std::cout << "  边界点(8,10): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
            
            // 测试外部点（应该不在禁飞区内）
            check_result = map.is_point_inside_any_nfz_2d({5, 5});
            std::cout << "  外部点(5,5): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
            
        } else {
            std::cout << "✗ 边界厚度 " << thickness << " 的正方形禁飞区添加失败" << std::endl;
        }
        
        std::cout << std::endl;
    }
    
    // 测试2：创建一个带偏移的多边形禁飞区
    std::cout << "\n--- 测试2：带偏移的多边形禁飞区 ---" << std::endl;
    
    // 定义一个三角形
    std::vector<std::pair<float, float>> triangle_vertices = {
        {5.0f, 5.0f},   // 底边左端点
        {15.0f, 5.0f},  // 底边右端点
        {10.0f, 15.0f}  // 顶点
    };
    
    auto result2 = map.add_hollow_polygonal_no_fly_zone_grid(
        triangle_vertices,
        "test_triangle_offset",
        2.0f,        // 向外偏移2个栅格
        2,           // 边界厚度2（向内）
        empty_paths
    );
    
    if (result2.first) {
        std::cout << "✓ 带偏移的三角形禁飞区添加成功" << std::endl;
        
        // 测试几何检查功能
        std::string check_result;
        
        // 测试原始三角形内部的点（应该在禁飞区内，因为偏移扩大了范围）
        check_result = map.is_point_inside_any_nfz_2d({10, 8});
        std::cout << "  原始三角形内部点(10,8): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
        
        // 测试偏移后的边界附近点
        check_result = map.is_point_inside_any_nfz_2d({3, 5});
        std::cout << "  偏移边界点(3,5): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
        
        // 测试远离的点
        check_result = map.is_point_inside_any_nfz_2d({1, 1});
        std::cout << "  远离点(1,1): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
        
    } else {
        std::cout << "✗ 带偏移的三角形禁飞区添加失败" << std::endl;
    }
    
    // 测试3：验证几何信息存储的正确性
    std::cout << "\n--- 测试3：验证几何信息存储 ---" << std::endl;
    
    // 创建一个简单的矩形
    std::vector<std::pair<float, float>> rect_vertices = {
        {2.0f, 2.0f},
        {6.0f, 2.0f},
        {6.0f, 6.0f},
        {2.0f, 6.0f}
    };
    
    auto result3 = map.add_hollow_polygonal_no_fly_zone_grid(
        rect_vertices,
        "test_rect_geometry",
        1.0f,        // 向外偏移1个栅格
        1,           // 边界厚度1
        empty_paths
    );
    
    if (result3.first) {
        std::cout << "✓ 矩形禁飞区添加成功" << std::endl;
        
        // 测试几何信息是否正确存储了最外层边界
        // 原始矩形是2x2到6x6，偏移1后应该是1x1到7x7
        std::string check_result;
        
        // 测试偏移后的边界点
        check_result = map.is_point_inside_any_nfz_2d({1, 4}); // 左边界
        std::cout << "  偏移后左边界(1,4): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
        
        check_result = map.is_point_inside_any_nfz_2d({7, 4}); // 右边界
        std::cout << "  偏移后右边界(7,4): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
        
        check_result = map.is_point_inside_any_nfz_2d({4, 1}); // 下边界
        std::cout << "  偏移后下边界(4,1): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
        
        check_result = map.is_point_inside_any_nfz_2d({4, 7}); // 上边界
        std::cout << "  偏移后上边界(4,7): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
        
        // 测试中心点（应该在禁飞区内）
        check_result = map.is_point_inside_any_nfz_2d({4, 4});
        std::cout << "  中心点(4,4): " << (check_result.empty() ? "不在禁飞区" : "在禁飞区: " + check_result) << std::endl;
        
    } else {
        std::cout << "✗ 矩形禁飞区添加失败" << std::endl;
    }
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    std::cout << "边界厚度向内扩展功能测试完成！" << std::endl;
    
    return 0;
}
