import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors
import time


class Visualizer3D:
    def __init__(self, map3d):
        """
        Initialize the 3D visualizer
        :param map3d: Map3D object containing the grid
        """
        self.map3d = map3d
        self.solution = None
        self.starts = None
        self.goals = None

        # Create figure and 3D axes
        self.fig = plt.figure(figsize=(10, 10))
        self.ax = self.fig.add_subplot(111, projection="3d")

        # Set axis limits
        self.ax.set_xlim(0, map3d.width)
        self.ax.set_ylim(0, map3d.height)
        self.ax.set_zlim(0, map3d.depth)

        # Set labels
        self.ax.set_xlabel("X")
        self.ax.set_ylabel("Y")
        self.ax.set_zlabel("Z")

        # Generate colors for agents
        self.colors = list(mcolors.TABLEAU_COLORS.values())

        # Enable grid
        self.ax.grid(True)

        # Plot obstacles (static)
        self.plot_obstacles()

        # 记录上次更新时间
        self.last_update_time = 0

    def plot_obstacles(self):
        """Plot obstacles and no-fly zones as cubes in 3D space"""
        # 绘制固定障碍物
        fixed_positions = self.map3d.obstacle_manager.get_positions("fixed")
        if fixed_positions:  # 如果有固定障碍物
            positions = np.array(list(fixed_positions))
            self.ax.scatter(
                positions[:, 1],  # x coordinates
                positions[:, 0],  # y coordinates
                positions[:, 2],  # z coordinates
                marker="s",
                s=80,
                color="gray",  # 固定障碍物用灰色
                alpha=0.3,
                zorder=1,
                label="Fixed Obstacles",
            )

        # 绘制所有禁飞区
        no_fly_zone_types = {
            name: info
            for name, info in self.map3d.obstacle_manager.get_all_types().items()
            if not name.startswith("fixed")
        }

        # 遍历每个禁飞区类型
        for zone_name, zone_info in no_fly_zone_types.items():
            zone_positions = self.map3d.obstacle_manager.get_positions(zone_name)
            if zone_positions:  # 如果有位置点
                positions = np.array(list(zone_positions))
                self.ax.scatter(
                    positions[:, 1],  # x coordinates
                    positions[:, 0],  # y coordinates
                    positions[:, 2],  # z coordinates
                    marker="s",
                    color="red",  # 禁飞区用红色
                    alpha=0.15,
                    s=60,
                    zorder=1,
                    label=f"No-fly zone: {zone_name}",
                )

    def plot_start_goal_points(self):
        """Plot start and goal points for all agents"""
        if self.starts and self.goals:
            for i, (start, goal) in enumerate(zip(self.starts, self.goals)):
                color = self.colors[i % len(self.colors)]

                # Plot start point (triangle marker)
                self.ax.scatter(
                    [start[1]],
                    [start[0]],
                    [start[2]],
                    marker="^",
                    s=200,
                    color=color,
                    label=f"Start {i}",
                    alpha=1.0,
                    zorder=2,
                )

                # Plot goal point (star marker)
                self.ax.scatter(
                    [goal[1]],
                    [goal[0]],
                    [goal[2]],
                    marker="*",
                    s=200,
                    color=color,
                    label=f"Goal {i}",
                    alpha=1.0,
                    zorder=2,
                )

    def plot_paths(self):
        """Plot all agent paths"""
        if not self.solution:
            return

        current_time = int(time.time())

        for agent_id, path in self.solution.items():
            path = path[0]  # 获取完整路径点
            if not path or len(path) == 0:  # 跳过空路径
                continue

            # 使用哈希值来确保任何类型的agent_id都能获得一个稳定的颜色
            color_index = hash(str(agent_id)) % len(self.colors)
            color = self.colors[color_index]

            # Convert path to coordinates
            try:
                if isinstance(path[0], (list, tuple)):  # If path contains tuples
                    coords = np.array([[pos[1], pos[0], pos[2]] for pos in path])
                    times = np.array(range(len(path)))
                else:  # If path contains GridNode3D objects
                    coords = np.array([[node.x, node.y, node.z] for node in path])
                    times = np.array([node.t for node in path])
            except (IndexError, AttributeError) as e:
                print(f"Warning: Could not process path for agent {agent_id}: {e}")
                continue

            # 只显示从起点到当前时间的路径点
            valid_indices = times <= current_time
            past_coords = coords[valid_indices]

            if len(past_coords) > 0:
                # Plot path line (past path)
                self.ax.plot(
                    past_coords[:, 0],
                    past_coords[:, 1],
                    past_coords[:, 2],
                    color=color,
                    alpha=0.8,
                    linewidth=2,
                    linestyle="-",
                    zorder=3,
                )

                # Plot current position
                current_pos = past_coords[-1]  # 当前位置是最后一个过去的点
                self.ax.scatter(
                    [current_pos[0]],
                    [current_pos[1]],
                    [current_pos[2]],
                    color=color,
                    s=100,
                    label=f"Agent {agent_id}",
                    zorder=3,
                )

                # 如果智能体还没到达目标，用虚线显示剩余路径
                if current_time < times[-1]:
                    future_indices = times > current_time
                    future_coords = coords[future_indices]
                    if len(future_coords) > 0:
                        self.ax.plot(
                            future_coords[:, 0],
                            future_coords[:, 1],
                            future_coords[:, 2],
                            color=color,
                            alpha=0.3,
                            linewidth=1,
                            linestyle="--",
                            zorder=3,
                        )

    def update_visualization(self, solution, starts, goals):
        """Update the visualization with new data"""
        self.solution = solution
        self.starts = starts
        self.goals = goals

        current_time = int(time.time())

        # 每秒更新一次
        if current_time > self.last_update_time:
            self.last_update_time = current_time

            # Clear the axes except for obstacles
            self.ax.cla()

            # Reset view limits and labels
            self.ax.set_xlim(0, self.map3d.width)
            self.ax.set_ylim(0, self.map3d.height)
            self.ax.set_zlim(0, self.map3d.depth)

            self.ax.set_xlabel("X")
            self.ax.set_ylabel("Y")
            self.ax.set_zlabel("Z")

            # Plot all elements
            self.plot_obstacles()
            self.plot_start_goal_points()
            self.plot_paths()

            # Add legend at fixed position (upper right)
            self.ax.legend(loc="upper right", bbox_to_anchor=(1, 1))

            # Update display
            self.fig.canvas.draw()
            self.fig.canvas.flush_events()


def visualize_solution(map3d, solution, starts, goals, block=True):
    """
    Convenience function to visualize a solution
    :param map3d: Map3D object
    :param solution: Dictionary mapping agent_id to (path, length)
    :param starts: List of start positions
    :param goals: List of goal positions
    :param block: Whether to block execution until window is closed
    """
    # 确保交互模式开启
    # plt.ion()

    # 创建可视化器并更新
    vis = Visualizer3D(map3d)
    vis.update_visualization(solution, starts, goals)

    # 强制显示图像
    # plt.figure(vis.fig.number)
    # plt.draw()

    # if block:
    #     # 如果需要阻塞，切换到非交互模式并显示
    #     plt.ioff()
    #     plt.show()


def visualize_static_elements(map3d, solution, starts, goals):
    """
    静态显示禁飞区、航路、起飞点和终点
    :param map3d: Map3D object
    :param solution: Dictionary mapping agent_id to (path, length)
    :param starts: List of start positions
    :param goals: List of goal positions
    """
    # 创建图形和3D坐标轴
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection="3d")

    # 设置坐标轴范围
    ax.set_xlim(0, map3d.get_width())
    ax.set_ylim(0, map3d.get_height())
    ax.set_zlim(0, map3d.get_depth())

    # 设置标签
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_zlabel("Z")

    # 启用网格
    ax.grid(True)

    # 生成颜色列表
    colors = list(mcolors.TABLEAU_COLORS.values())

    # 绘制禁飞区
    # 绘制固定障碍物
    fixed_positions = map3d.get_obstacle_manager().get_positions_for_type("fixed")
    # fixed_positions = None
    if fixed_positions:  # 如果有固定障碍物
        positions = np.array(list(fixed_positions))
        ax.scatter(
            positions[:, 1],  # x coordinates
            positions[:, 0],  # y coordinates
            positions[:, 2],  # z coordinates
            marker="s",
            s=1,
            color="gray",  # 固定障碍物用灰色
            alpha=0.1,
            zorder=1,
            label="Fixed Obstacles",
        )

    # 绘制所有禁飞区
    no_fly_zone_types = [
        name
        for name in map3d.get_obstacle_manager().get_all_type_names()
        if not name.startswith("fixed")
    ]

    # 遍历每个禁飞区类型
    for zone_name in no_fly_zone_types:
        zone_positions = map3d.get_obstacle_manager().get_positions_for_type(zone_name)
        if zone_positions:  # 如果有位置点
            positions = np.array(list(zone_positions))
            ax.scatter(
                positions[:, 1],  # x coordinates
                positions[:, 0],  # y coordinates
                positions[:, 2],  # z coordinates
                marker="s",
                color="red",  # 禁飞区用红色
                alpha=0.1,
                s=2,
                zorder=1,
                label=f"No-fly zone: {zone_name}",
            )

    # 绘制起点和终点
    if starts and goals:
        for i, (start, goal) in enumerate(zip(starts, goals)):
            color = colors[i % len(colors)]

            # 绘制起点（三角形标记）
            ax.scatter(
                [start[1]],
                [start[0]],
                [start[2]],
                marker="^",
                s=200,
                color=color,
                label=f"Start {i}",
                alpha=1.0,
                zorder=2,
            )

            # 绘制终点（星形标记）
            ax.scatter(
                [goal[1]],
                [goal[0]],
                [goal[2]],
                marker="*",
                s=200,
                color=color,
                label=f"Goal {i}",
                alpha=1.0,
                zorder=2,
            )

    # 绘制所有路径
    if solution:
        for agent_id, pathes in solution.items():
            path = pathes["path"]  # 获取完整路径点
            if not path or len(path) == 0:  # 跳过空路径
                continue

            # 使用哈希值来确保任何类型的agent_id都能获得一个稳定的颜色
            color_index = hash(str("0")) % len(colors)
            color = colors[color_index]

            # 转换路径为坐标
            try:
                if isinstance(path[0], (list, tuple)):  # 如果路径包含元组
                    coords = np.array([[pos[1], pos[0], pos[2]] for pos in path])
                else:  # 如果路径包含GridNode3D对象
                    coords = np.array([[node.x, node.y, node.z] for node in path])
            except (IndexError, AttributeError) as e:
                print(f"Warning: Could not process path for agent {'0'}: {e}")
                continue

            # 绘制完整路径线
            ax.plot(
                coords[:, 0],
                coords[:, 1],
                coords[:, 2],
                color=color,
                alpha=0.8,
                linewidth=2,
                linestyle="-",
                zorder=3,
                label=f"Path {0}",
            )

    # 添加图例
    ax.legend(loc="upper right", bbox_to_anchor=(1, 1))

    # 设置标题
    ax.set_title("No-fly Zones, Paths, Start and Goal Points")

    # 显示图形
    plt.tight_layout()
    plt.show()
