// 障碍物类型管理器
#pragma once

#include "common_types.h" // 引入Point3D等基础类型定义
#include <string>         // 用于字符串处理
#include <vector>         // 用于存储数据
#include <unordered_map>  // 用于映射关系
#include <set>            // 用于函数参数和返回值
#include <unordered_set>  // 用于内部高效存储
#include <stdexcept>      // 用于异常处理
#include <memory>         // 用于智能指针

// 禁飞区几何信息基类
struct NFZGeometry
{
    virtual ~NFZGeometry() = default;
    virtual bool contains_point_2d(float x, float y) const = 0;
};

// 圆柱形禁飞区几何信息
struct CylindricalNFZGeometry : public NFZGeometry
{
    float center_x;
    float center_y;
    float radius;

    CylindricalNFZGeometry(float cx, float cy, float r)
        : center_x(cx), center_y(cy), radius(r) {}

    bool contains_point_2d(float x, float y) const override
    {
        float dx = x - center_x;
        float dy = y - center_y;
        return (dx * dx + dy * dy) <= (radius * radius);
    }
};

// 多边形禁飞区几何信息
struct PolygonalNFZGeometry : public NFZGeometry
{
    std::vector<std::pair<float, float>> vertices;

    PolygonalNFZGeometry(const std::vector<std::pair<float, float>> &verts)
        : vertices(verts) {}

    bool contains_point_2d(float x, float y) const override
    {
        // 使用优化的射线投射算法判断点是否在多边形内部
        // 边界上的点返回false，避免除零错误，针对频繁调用进行性能优化
        int intersections = 0;
        size_t n = vertices.size();

        for (size_t i = 0; i < n; ++i)
        {
            size_t j = (i + 1) % n;
            float xi = vertices[i].first, yi = vertices[i].second;
            float xj = vertices[j].first, yj = vertices[j].second;

            // 跳过水平边，避免除零错误
            if (yi == yj)
            {
                continue;
            }

            // 确保yi < yj，简化逻辑
            if (yi > yj)
            {
                std::swap(xi, xj);
                std::swap(yi, yj);
            }

            // 检查射线是否可能与边相交
            // 使用不对称条件 y > yi && y <= yj 来避免顶点重复计数
            // 同时自然排除边界上的点
            if (y > yi && y <= yj)
            {
                // 计算交点的x坐标
                float x_intersect = xi + (xj - xi) * (y - yi) / (yj - yi);

                // 如果交点在测试点的右侧，计数+1
                if (x < x_intersect)
                {
                    intersections++;
                }
            }
        }

        return (intersections % 2) == 1;
    }
};

// 障碍物类型信息结构体
struct ObstacleTypeInfo
{
    std::string description; // 障碍物类型描述
    std::string created_at;  // 创建时间（字符串格式，可升级为std::chrono::time_point）
    NFZGeometry *geometry;   // 禁飞区几何信息（可选，原始指针）

    // 默认构造函数
    ObstacleTypeInfo() : geometry(nullptr) {}

    // 带参数的构造函数
    ObstacleTypeInfo(const std::string &desc, const std::string &created, NFZGeometry *geom = nullptr)
        : description(desc), created_at(created), geometry(geom) {}

    // 析构函数
    ~ObstacleTypeInfo()
    {
        delete geometry;
        geometry = nullptr;
    }

    // 拷贝构造函数（深拷贝）
    ObstacleTypeInfo(const ObstacleTypeInfo &other)
        : description(other.description), created_at(other.created_at), geometry(nullptr)
    {
        if (other.geometry)
        {
            // 根据类型进行深拷贝
            if (auto *cyl = dynamic_cast<const CylindricalNFZGeometry *>(other.geometry))
            {
                geometry = new CylindricalNFZGeometry(cyl->center_x, cyl->center_y, cyl->radius);
            }
            else if (auto *poly = dynamic_cast<const PolygonalNFZGeometry *>(other.geometry))
            {
                geometry = new PolygonalNFZGeometry(poly->vertices);
            }
        }
    }

    // 拷贝赋值操作符（深拷贝）
    ObstacleTypeInfo &operator=(const ObstacleTypeInfo &other)
    {
        if (this != &other)
        {
            delete geometry;
            geometry = nullptr;

            description = other.description;
            created_at = other.created_at;

            if (other.geometry)
            {
                // 根据类型进行深拷贝
                if (auto *cyl = dynamic_cast<const CylindricalNFZGeometry *>(other.geometry))
                {
                    geometry = new CylindricalNFZGeometry(cyl->center_x, cyl->center_y, cyl->radius);
                }
                else if (auto *poly = dynamic_cast<const PolygonalNFZGeometry *>(other.geometry))
                {
                    geometry = new PolygonalNFZGeometry(poly->vertices);
                }
            }
        }
        return *this;
    }

    // 移动构造函数
    ObstacleTypeInfo(ObstacleTypeInfo &&other) noexcept
        : description(std::move(other.description)),
          created_at(std::move(other.created_at)),
          geometry(other.geometry)
    {
        other.geometry = nullptr;
    }

    // 移动赋值操作符
    ObstacleTypeInfo &operator=(ObstacleTypeInfo &&other) noexcept
    {
        if (this != &other)
        {
            delete geometry;
            description = std::move(other.description);
            created_at = std::move(other.created_at);
            geometry = other.geometry;
            other.geometry = nullptr;
        }
        return *this;
    }
};

// 障碍物类型管理器类
// 用于管理和维护各种禁飞区的类型信息和空间占用情况
class ObstacleTypeManager
{
public:
    // 障碍物类型管理器构造函数
    // @param height: 地图高度（栅格数）
    // @param width: 地图宽度（栅格数）
    // @param depth: 地图深度（栅格数）
    ObstacleTypeManager(int height, int width, int depth)
        : height_(height), width_(width), depth_(depth), _positions(), _all_non_traversable_nodes() {}

    // 添加新的障碍物类型
    // @param name: 障碍物类型名称
    // @param description: 障碍物类型描述
    // @param created_at_str: 创建时间字符串
    // @throw std::runtime_error 如果类型已存在
    void add_obstacle_type(const std::string &name, const std::string &description, const std::string &created_at_str)
    {
        if (_types.count(name))
        {
            throw std::runtime_error("Obstacle type '" + name + "' already exists.");
        }
        auto result = _types.emplace(name, ObstacleTypeInfo(description, created_at_str, nullptr));
        if (!result.second)
        {
            throw std::runtime_error("Failed to add obstacle type '" + name + "'.");
        }
        _positions[name] = {}; // 初始化空坐标集合
    }

    // 添加新的禁飞区类型（带几何信息）
    // @param name: 禁飞区类型名称
    // @param description: 禁飞区类型描述
    // @param created_at_str: 创建时间字符串
    // @param geometry: 禁飞区几何信息
    // @throw std::runtime_error 如果类型已存在
    void add_nfz_type(const std::string &name, const std::string &description,
                      const std::string &created_at_str, std::unique_ptr<NFZGeometry> geometry)
    {
        if (_types.count(name))
        {
            throw std::runtime_error("Obstacle type '" + name + "' already exists.");
        }
        auto result = _types.emplace(name, ObstacleTypeInfo(description, created_at_str, geometry.release()));
        if (!result.second)
        {
            throw std::runtime_error("Failed to add NFZ type '" + name + "'.");
        }
        _positions[name] = {}; // 初始化空坐标集合
    }

    // 移除指定的障碍物类型及其所有坐标
    // @param name: 要移除的障碍物类型名称
    void remove_obstacle_type(const std::string &name)
    {
        if (!_types.count(name))
        {
            return; // 类型不存在时直接返回
        }
        _types.erase(name);
        _positions.erase(name);
        rebuild_all_non_traversable_nodes(); // 更新不可通行节点集合
    }

    // 为指定类型添加障碍物坐标
    // @param type_name: 障碍物类型名称
    // @param positions_to_add: 要添加的坐标集合
    // @throw std::runtime_error 如果类型不存在
    void add_obstacle_positions(const std::string &type_name, const std::set<Point3D> &positions_to_add)
    {
        if (!_types.count(type_name))
        {
            throw std::runtime_error("Obstacle type '" + type_name + "' does not exist, cannot add coordinates.");
        }
        _positions[type_name].insert(positions_to_add.begin(), positions_to_add.end());
        rebuild_all_non_traversable_nodes(); // 更新不可通行节点集合
    }

    // 从指定类型中移除障碍物坐标
    // @param type_name: 障碍物类型名称
    // @param positions_to_remove: 要移除的坐标集合
    void remove_obstacle_positions(const std::string &type_name, const std::set<Point3D> &positions_to_remove)
    {
        if (!_positions.count(type_name))
        {
            return; // 类型不存在时直接返回
        }
        for (const auto &pos : positions_to_remove)
        {
            _positions[type_name].erase(pos);
        }
        rebuild_all_non_traversable_nodes(); // 更新不可通行节点集合
    }

    // 获取所有不可通行节点的集合
    // @return: 返回所有被标记为障碍物的节点集合
    const std::unordered_set<Point3D, Point3DHash> &get_all_non_traversable_nodes() const
    {
        return _all_non_traversable_nodes;
    }

    // 获取指定类型的所有障碍物坐标
    // @param type_name: 障碍物类型名称
    // @return: 返回该类型的所有坐标点集合
    std::unordered_set<Point3D, Point3DHash> get_positions_for_type(const std::string &type_name) const
    {
        if (_positions.count(type_name))
        {
            return _positions.at(type_name);
        }
        return {}; // 类型不存在时返回空集合
    }

    // 获取指定类型的信息
    // @param type_name: 障碍物类型名称
    // @return: 返回障碍物类型信息的常量引用
    // @throw std::runtime_error 如果类型不存在
    const ObstacleTypeInfo &get_type_info(const std::string &type_name) const
    {
        if (_types.count(type_name))
        {
            return _types.at(type_name);
        }
        throw std::runtime_error("Obstacle type '" + type_name + "' does not exist.");
    }

    // 检查指定点是否为障碍物
    // @param pos: 待检查的位置
    // @return: 如果该点是任意类型的障碍物返回true，否则返回false
    bool is_obstacle(const Point3D &pos) const
    {
        return _all_non_traversable_nodes.count(pos);
    }

    // 检查指定点是否为特定类型的障碍物
    // @param type_name: 障碍物类型名称
    // @param pos: 待检查的位置
    // @return: 如果该点是指定类型的障碍物返回true，否则返回false
    bool is_obstacle_of_type(const std::string &type_name, const Point3D &pos) const
    {
        if (_positions.count(type_name))
        {
            return _positions.at(type_name).count(pos);
        }
        return false;
    }

    // 获取所有已定义的障碍物类型名称
    // @return: 返回所有障碍物类型名称的列表
    std::vector<std::string> get_all_type_names() const
    {
        std::vector<std::string> names;
        for (const auto &[type_name, type_info] : _types)
        {
            names.push_back(type_name);
        }
        return names;
    }

    // 检查指定二维点是否位于任何禁飞区内
    // @param x: X坐标（栅格）
    // @param y: Y坐标（栅格）
    // @return: 如果点位于任何NFZ内部，则返回该NFZ的名称；如果点不在任何NFZ内部，则返回空字符串
    std::string is_point_inside_any_nfz_2d(float x, float y) const
    {
        for (const auto &[type_name, type_info] : _types)
        {
            if (type_info.geometry)
            {
                // 调试信息：检查几何信息是否存在
                bool contains = type_info.geometry->contains_point_2d(x, y);
                if (contains)
                {
                    return type_name; // 找到匹配的禁飞区，立即返回
                }
            }
        }
        return ""; // 没有找到任何匹配的禁飞区
    }

    // 调试函数：获取禁飞区的几何信息
    std::string debug_nfz_geometry_info() const
    {
        std::string info = "NFZ Geometry Debug Info:\n";
        for (const auto &[type_name, type_info] : _types)
        {
            info += "  Zone: " + type_name + "\n";
            if (type_info.geometry)
            {
                if (auto *poly = dynamic_cast<const PolygonalNFZGeometry *>(type_info.geometry))
                {
                    info += "    Type: Polygonal\n";
                    info += "    Vertices: ";
                    for (size_t i = 0; i < poly->vertices.size(); ++i)
                    {
                        info += "(" + std::to_string(poly->vertices[i].first) + "," +
                                std::to_string(poly->vertices[i].second) + ")";
                        if (i < poly->vertices.size() - 1)
                            info += ", ";
                    }
                    info += "\n";
                }
                else if (auto *cyl = dynamic_cast<const CylindricalNFZGeometry *>(type_info.geometry))
                {
                    info += "    Type: Cylindrical\n";
                    info += "    Center: (" + std::to_string(cyl->center_x) + "," +
                            std::to_string(cyl->center_y) + ")\n";
                    info += "    Radius: " + std::to_string(cyl->radius) + "\n";
                }
            }
            else
            {
                info += "    No geometry info\n";
            }
        }
        return info;
    }

    // 获取指定位置的所有障碍物类型
    // @param pos: 待检查的位置
    // @return: 返回该位置上的所有障碍物类型名称列表
    std::vector<std::string> get_type_at_position(const Point3D &pos) const
    {
        std::vector<std::string> types;
        for (const auto &[type_name, positions] : _positions)
        {
            if (positions.count(pos) > 0)
            {
                types.push_back(type_name);
            }
        }
        return types;
    }

private:
    // 地图尺寸
    int height_; // 地图高度（栅格数）
    int width_;  // 地图宽度（栅格数）
    int depth_;  // 地图深度（栅格数）

    // 核心数据结构
    std::unordered_map<std::string, ObstacleTypeInfo> _types;                             // 存储障碍物类型信息
    std::unordered_map<std::string, std::unordered_set<Point3D, Point3DHash>> _positions; // 存储各类型障碍物的坐标集合
    std::unordered_set<Point3D, Point3DHash> _all_non_traversable_nodes;                  // 存储所有不可通行节点的集合

    // 重建不可通行节点集合
    // 当任何类型的障碍物坐标发生变化时，需要调用此方法更新全局的不可通行节点集合
    void rebuild_all_non_traversable_nodes()
    {
        _all_non_traversable_nodes.clear(); // 清空当前集合
        for (const auto &pair : _positions)
        {
            // 将所有类型的障碍物坐标添加到不可通行节点集合中
            _all_non_traversable_nodes.insert(pair.second.begin(), pair.second.end());
        }
    }
};
