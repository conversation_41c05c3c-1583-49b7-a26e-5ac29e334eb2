#include "../include/ObstacleTypeManager.h"
#include <iostream>
#include <vector>
#include <cassert>
#include <chrono>

void test_simple_square()
{
    std::cout << "测试简单正方形..." << std::endl;

    // 创建一个简单的正方形: (0,0), (2,0), (2,2), (0,2)
    std::vector<std::pair<float, float>> square_vertices = {
        {0.0f, 0.0f}, {2.0f, 0.0f}, {2.0f, 2.0f}, {0.0f, 2.0f}};

    PolygonalNFZGeometry square(square_vertices);

    // 测试内部点
    assert(square.contains_point_2d(1.0f, 1.0f) == true);
    std::cout << "✓ 内部点 (1,1) 正确识别为在多边形内" << std::endl;

    // 测试外部点
    assert(square.contains_point_2d(3.0f, 1.0f) == false);
    assert(square.contains_point_2d(-1.0f, 1.0f) == false);
    assert(square.contains_point_2d(1.0f, 3.0f) == false);
    assert(square.contains_point_2d(1.0f, -1.0f) == false);
    std::cout << "✓ 外部点正确识别为不在多边形内" << std::endl;

    // 测试边界上的点（应该返回false）
    assert(square.contains_point_2d(0.0f, 1.0f) == false); // 左边
    assert(square.contains_point_2d(2.0f, 1.0f) == false); // 右边
    assert(square.contains_point_2d(1.0f, 0.0f) == false); // 下边
    assert(square.contains_point_2d(1.0f, 2.0f) == false); // 上边
    std::cout << "✓ 边界上的点正确识别为不在多边形内" << std::endl;

    // 测试顶点（应该返回false）
    assert(square.contains_point_2d(0.0f, 0.0f) == false);
    assert(square.contains_point_2d(2.0f, 0.0f) == false);
    assert(square.contains_point_2d(2.0f, 2.0f) == false);
    assert(square.contains_point_2d(0.0f, 2.0f) == false);
    std::cout << "✓ 顶点正确识别为不在多边形内" << std::endl;
}

void test_triangle()
{
    std::cout << "\n测试三角形..." << std::endl;

    // 创建一个三角形: (0,0), (4,0), (2,3)
    std::vector<std::pair<float, float>> triangle_vertices = {
        {0.0f, 0.0f}, {4.0f, 0.0f}, {2.0f, 3.0f}};

    PolygonalNFZGeometry triangle(triangle_vertices);

    // 测试内部点
    assert(triangle.contains_point_2d(2.0f, 1.0f) == true);
    assert(triangle.contains_point_2d(1.5f, 1.5f) == true);
    std::cout << "✓ 内部点正确识别为在三角形内" << std::endl;

    // 测试外部点
    assert(triangle.contains_point_2d(-1.0f, 1.0f) == false);
    assert(triangle.contains_point_2d(5.0f, 1.0f) == false);
    assert(triangle.contains_point_2d(2.0f, 4.0f) == false);
    std::cout << "✓ 外部点正确识别为不在三角形内" << std::endl;

    // 测试边界上的点
    assert(triangle.contains_point_2d(2.0f, 0.0f) == false); // 底边中点
    assert(triangle.contains_point_2d(1.0f, 1.5f) == false); // 左边上的点
    std::cout << "✓ 边界上的点正确识别为不在三角形内" << std::endl;
}

void test_concave_polygon()
{
    std::cout << "\n测试凹多边形..." << std::endl;

    // 创建一个L形状的凹多边形
    std::vector<std::pair<float, float>> l_shape_vertices = {
        {0.0f, 0.0f}, {3.0f, 0.0f}, {3.0f, 2.0f}, {1.0f, 2.0f}, {1.0f, 4.0f}, {0.0f, 4.0f}};

    PolygonalNFZGeometry l_shape(l_shape_vertices);

    // 测试内部点
    assert(l_shape.contains_point_2d(0.5f, 1.0f) == true); // 下部分内部
    assert(l_shape.contains_point_2d(0.5f, 3.0f) == true); // 上部分内部
    assert(l_shape.contains_point_2d(2.0f, 1.0f) == true); // 右部分内部
    std::cout << "✓ L形状内部点正确识别" << std::endl;

    // 测试凹陷区域的点（应该在外部）
    assert(l_shape.contains_point_2d(2.0f, 3.0f) == false);
    std::cout << "✓ L形状凹陷区域点正确识别为外部" << std::endl;
}

void test_horizontal_edges()
{
    std::cout << "\n测试包含水平边的多边形..." << std::endl;

    // 创建包含水平边的多边形
    std::vector<std::pair<float, float>> rect_with_horizontal = {
        {0.0f, 0.0f}, {3.0f, 0.0f}, {3.0f, 1.0f}, {2.0f, 1.0f}, {2.0f, 2.0f}, {0.0f, 2.0f}};

    PolygonalNFZGeometry poly(rect_with_horizontal);

    // 测试与水平边平行的射线
    assert(poly.contains_point_2d(1.0f, 1.5f) == true);  // 内部点
    assert(poly.contains_point_2d(2.5f, 1.5f) == false); // 外部点
    std::cout << "✓ 包含水平边的多边形处理正确" << std::endl;
}

int main()
{
    std::cout << "开始测试优化后的射线投射算法..." << std::endl;
    std::cout << "=====================================" << std::endl;

    try
    {
        test_simple_square();
        test_triangle();
        test_concave_polygon();
        test_horizontal_edges();

        std::cout << "\n=====================================" << std::endl;
        std::cout << "🎉 所有测试通过！算法工作正常。" << std::endl;

        // 性能测试
        std::cout << "\n进行简单性能测试..." << std::endl;
        std::vector<std::pair<float, float>> test_poly = {
            {0.0f, 0.0f}, {10.0f, 0.0f}, {10.0f, 10.0f}, {0.0f, 10.0f}};
        PolygonalNFZGeometry perf_test(test_poly);

        auto start = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 100000; ++i)
        {
            perf_test.contains_point_2d(5.0f, 5.0f);
        }
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

        std::cout << "10万次调用耗时: " << duration.count() << " 微秒" << std::endl;
        std::cout << "平均每次调用: " << (double)duration.count() / 100000.0 << " 微秒" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
