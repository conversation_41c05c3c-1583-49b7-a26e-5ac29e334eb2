#!/usr/bin/env python3
"""
测试新的is_point_inside_any_nfz_2d函数的Python脚本
"""

import sys
import os

# 添加Python绑定路径
sys.path.append('python_bindings')

try:
    import pathfinding_cpp as pf
    print("成功导入pathfinding_cpp模块")
except ImportError as e:
    print(f"导入pathfinding_cpp模块失败: {e}")
    print("请确保已正确编译Python绑定")
    sys.exit(1)

def test_nfz_check():
    """测试禁飞区检查功能"""
    print("\n=== 测试禁飞区检查功能 ===")
    
    # 创建网格转换器
    converter = pf.create_grid_converter(
        lat_size=0.001,   # 纬度分辨率
        lon_size=0.001,   # 经度分辨率  
        alt_size=100.0,   # 高度分辨率
        min_lat=39.9,     # 最小纬度
        min_lon=116.4,    # 最小经度
        min_alt=0.0       # 最小高度
    )
    
    # 创建10x10x5的地图
    map3d = pf.Map3D(10, 10, 5, converter)
    
    print("地图创建成功")
    
    # 测试1：添加圆柱形禁飞区
    print("\n--- 测试1：圆柱形禁飞区 ---")
    
    success, conflicts = map3d.add_solid_cylindrical_no_fly_zone_grid(
        center_x_grid=5.0,
        center_y_grid=5.0,
        radius_in_grids=2.0,
        zone_name="test_cylinder",
        planned_paths_dict={},
        boundary_thickness_grids=0  # 实心圆柱体
    )
    
    if success:
        print("圆柱形禁飞区添加成功")
        
        # 测试不同位置的点
        test_points = [
            (5, 5, "中心点"),
            (6, 5, "半径内的点"),
            (8, 5, "半径外的点"),
            (0, 0, "远离的点")
        ]
        
        for y, x, description in test_points:
            result = map3d.is_point_inside_any_nfz_2d((y, x))
            status = "在禁飞区内" if result else "不在禁飞区内"
            zone_name = f" (禁飞区: {result})" if result else ""
            print(f"点({y},{x}) - {description}: {status}{zone_name}")
    else:
        print("圆柱形禁飞区添加失败")
    
    # 测试2：添加多边形禁飞区
    print("\n--- 测试2：多边形禁飞区 ---")
    
    # 定义一个正方形多边形
    polygon_vertices = [
        (1.0, 1.0),  # (x, y) 格式
        (3.0, 1.0),
        (3.0, 3.0),
        (1.0, 3.0)
    ]
    
    success, conflicts = map3d.add_hollow_polygonal_no_fly_zone_grid(
        polygon_vertices=polygon_vertices,
        zone_name="test_polygon",
        offset_grids=0.0,
        boundary_thickness_grids=1,
        planned_paths_dict={}
    )
    
    if success:
        print("多边形禁飞区添加成功")
        
        # 测试不同位置的点
        test_points = [
            (2, 2, "多边形内部点"),
            (4, 4, "多边形外部点"),
            (1, 1, "多边形边界点"),
            (7, 7, "远离多边形的点")
        ]
        
        for y, x, description in test_points:
            result = map3d.is_point_inside_any_nfz_2d((y, x))
            status = "在禁飞区内" if result else "不在禁飞区内"
            zone_name = f" (禁飞区: {result})" if result else ""
            print(f"点({y},{x}) - {description}: {status}{zone_name}")
    else:
        print("多边形禁飞区添加失败")
    
    # 测试3：边界检查
    print("\n--- 测试3：边界检查 ---")
    
    boundary_test_points = [
        (-1, 5, "超出左边界"),
        (15, 5, "超出右边界"),
        (5, -1, "超出下边界"),
        (5, 15, "超出上边界")
    ]
    
    for y, x, description in boundary_test_points:
        try:
            result = map3d.is_point_inside_any_nfz_2d((y, x))
            status = "在禁飞区内" if result else "不在禁飞区内"
            zone_name = f" (禁飞区: {result})" if result else ""
            print(f"点({y},{x}) - {description}: {status}{zone_name}")
        except Exception as e:
            print(f"点({y},{x}) - {description}: 发生错误 - {e}")
    
    print("\n=== 测试完成 ===")

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    converter = pf.create_grid_converter(0.001, 0.001, 100.0, 39.9, 116.4, 0.0)
    map3d = pf.Map3D(10, 10, 5, converter)
    
    # 测试无效的参数格式
    try:
        result = map3d.is_point_inside_any_nfz_2d((5,))  # 只有一个坐标
        print("错误：应该抛出异常但没有")
    except Exception as e:
        print(f"正确捕获异常: {e}")
    
    try:
        result = map3d.is_point_inside_any_nfz_2d((5, 5, 5))  # 三个坐标
        print("错误：应该抛出异常但没有")
    except Exception as e:
        print(f"正确捕获异常: {e}")

if __name__ == "__main__":
    test_nfz_check()
    test_error_handling()
