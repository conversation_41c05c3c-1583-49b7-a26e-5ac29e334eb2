#include "cpp_implementation/include/Map3D.h"
#include "cpp_implementation/include/GridConverter.h"
#include <iostream>
#include <iomanip>

int main() {
    std::cout << "测试几何信息保存和使用..." << std::endl;
    
    // 使用您提供的实际坐标范围创建网格转换器
    // 禁飞区经度范围：118.60-118.66，纬度范围：32.01-32.10
    // 测试点：118.707, 32.098
    
    // 创建一个覆盖该区域的网格转换器
    GridConverter converter(
        32.0,    // min_lat
        118.5,   // min_lon  
        0.001,   // lat_size (每个栅格约111米)
        0.001,   // lon_size (每个栅格约111米)
        200,     // lat_grids (覆盖0.2度纬度)
        300      // lon_grids (覆盖0.3度经度)
    );
    
    // 创建地图
    Map3D map(200, 300, 5, converter);
    
    std::cout << "地图创建成功，覆盖范围：" << std::endl;
    std::cout << "  纬度：32.0 - 32.2" << std::endl;
    std::cout << "  经度：118.5 - 118.8" << std::endl;
    
    // 定义您提供的禁飞区顶点
    std::vector<GeoCoordinate> nfz_vertices = {
        {32.08819658981454, 118.63054181099031, 0.0},
        {32.05967965190471, 118.60864252291884, 0.0},
        {32.01886072659458, 118.60775551024604, 0.0},
        {32.02418418221094, 118.63193987990937, 0.0},
        {32.06448415244297, 118.64790128973038, 0.0},
        {32.102198319535155, 118.65991741736788, 0.0}
    };
    
    std::cout << "\n禁飞区顶点（地理坐标）：" << std::endl;
    for (size_t i = 0; i < nfz_vertices.size(); ++i) {
        std::cout << "  " << i << ": (" << std::fixed << std::setprecision(6) 
                  << nfz_vertices[i].lat << ", " << nfz_vertices[i].lon << ")" << std::endl;
    }
    
    // 转换为栅格坐标并显示
    std::cout << "\n禁飞区顶点（栅格坐标）：" << std::endl;
    for (size_t i = 0; i < nfz_vertices.size(); ++i) {
        GridPoint grid_pt = converter.geographic_to_grid(nfz_vertices[i]);
        std::cout << "  " << i << ": (" << std::fixed << std::setprecision(2)
                  << std::get<0>(grid_pt) << ", " << std::get<1>(grid_pt) << ")" << std::endl;
    }
    
    // 添加禁飞区
    std::map<std::string, std::vector<GridNode3D>> empty_paths;
    auto result = map.add_hollow_polygonal_no_fly_zone(
        nfz_vertices,
        "test_nfz",
        0.0,  // 无偏移
        1,    // 边界厚度1
        empty_paths
    );
    
    if (result.first) {
        std::cout << "\n✓ 禁飞区添加成功" << std::endl;
    } else {
        std::cout << "\n✗ 禁飞区添加失败" << std::endl;
        return 1;
    }
    
    // 测试您提供的点
    GeoCoordinate test_point = {32.098001586320805, 118.70694807576172, 180.0};
    
    std::cout << "\n测试点（地理坐标）：(" << std::fixed << std::setprecision(6)
              << test_point.lat << ", " << test_point.lon << ")" << std::endl;
    
    // 转换为栅格坐标
    GridPoint test_grid_pt = converter.geographic_to_grid(test_point);
    int test_lat_idx = static_cast<int>(std::round(std::get<1>(test_grid_pt)));
    int test_lon_idx = static_cast<int>(std::round(std::get<0>(test_grid_pt)));
    
    std::cout << "测试点（栅格坐标）：(" << test_lon_idx << ", " << test_lat_idx << ")" << std::endl;
    
    // 检查是否在地图边界内
    if (test_lon_idx < 0 || test_lon_idx >= 300 || test_lat_idx < 0 || test_lat_idx >= 200) {
        std::cout << "⚠️  测试点超出地图边界！" << std::endl;
        std::cout << "  地图边界：lon[0, 299], lat[0, 199]" << std::endl;
        std::cout << "  测试点：lon=" << test_lon_idx << ", lat=" << test_lat_idx << std::endl;
    } else {
        std::cout << "✓ 测试点在地图边界内" << std::endl;
    }
    
    // 使用我们的函数检查
    std::string check_result = map.is_point_inside_any_nfz_2d({test_lat_idx, test_lon_idx});
    std::cout << "\n检查结果：" << (check_result.empty() ? "不在禁飞区内" : "在禁飞区内: " + check_result) << std::endl;
    
    // 手动验证几何计算
    std::cout << "\n手动验证几何计算：" << std::endl;
    
    // 计算禁飞区的栅格坐标边界
    float min_lon_grid = 1000, max_lon_grid = -1000;
    float min_lat_grid = 1000, max_lat_grid = -1000;
    
    for (const auto& vertex : nfz_vertices) {
        GridPoint grid_pt = converter.geographic_to_grid(vertex);
        float lon_grid = std::get<0>(grid_pt);
        float lat_grid = std::get<1>(grid_pt);
        
        min_lon_grid = std::min(min_lon_grid, lon_grid);
        max_lon_grid = std::max(max_lon_grid, lon_grid);
        min_lat_grid = std::min(min_lat_grid, lat_grid);
        max_lat_grid = std::max(max_lat_grid, lat_grid);
    }
    
    std::cout << "禁飞区栅格边界：" << std::endl;
    std::cout << "  经度：[" << std::fixed << std::setprecision(2) << min_lon_grid << ", " << max_lon_grid << "]" << std::endl;
    std::cout << "  纬度：[" << min_lat_grid << ", " << max_lat_grid << "]" << std::endl;
    
    float test_lon_grid = std::get<0>(test_grid_pt);
    float test_lat_grid = std::get<1>(test_grid_pt);
    
    std::cout << "测试点栅格坐标：(" << test_lon_grid << ", " << test_lat_grid << ")" << std::endl;
    
    // 简单的边界框检查
    bool in_bbox = (test_lon_grid >= min_lon_grid && test_lon_grid <= max_lon_grid &&
                    test_lat_grid >= min_lat_grid && test_lat_grid <= max_lat_grid);
    
    std::cout << "边界框检查：" << (in_bbox ? "在边界框内" : "在边界框外") << std::endl;
    
    // 测试几个禁飞区内部的点
    std::cout << "\n测试禁飞区内部的几个点：" << std::endl;
    
    // 计算禁飞区中心点
    float center_lon = (min_lon_grid + max_lon_grid) / 2.0f;
    float center_lat = (min_lat_grid + max_lat_grid) / 2.0f;
    
    int center_lon_idx = static_cast<int>(std::round(center_lon));
    int center_lat_idx = static_cast<int>(std::round(center_lat));
    
    std::cout << "  中心点(" << center_lon_idx << ", " << center_lat_idx << "): ";
    std::string center_result = map.is_point_inside_any_nfz_2d({center_lat_idx, center_lon_idx});
    std::cout << (center_result.empty() ? "不在禁飞区内" : "在禁飞区内: " + center_result) << std::endl;
    
    return 0;
}
