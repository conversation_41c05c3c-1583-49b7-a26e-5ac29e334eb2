from typing import List, Dict, Optional
import json
import logging
import socket
from ..utils.logging import get_logger
from ..utils.exceptions import KafkaError
from ..config import settings  # Corrected import

# from ..core.map.map_handler_3d import Map3D
# from ..core.map.occupancy_map import OccupancyMap
from kafka import KafkaConsumer, KafkaProducer
from .message_handlers.route_handler import RouteMessageHandler
from .message_handlers.planning_handler import PlanningMessageHandler
from .message_handlers.no_fly_zone_handler import NoFlyZoneMessageHandler
from .message_handlers.approval_handler import ApprovalMessageHandler
from .message_handlers.state_handler import StateHandler
from .message_handlers.reroute_handler import RerouteMessageHandler

from pathfinding_cpp import Map3D, create_grid_converter, OccupancyMap

logger = get_logger(__name__)


class PathPlanningConsumer:
    """路径规划Kafka消费者"""

    def __init__(
        self,
        bootstrap_servers: Optional[List[str]] = None,
        request_topic: Optional[str] = None,
        response_topic: Optional[str] = None,
        response_topic_sora: Optional[str] = None,
        group_id: Optional[str] = None,
        init_kafka: bool = True,
    ):
        """
        初始化Kafka消费者

        Args:
            bootstrap_servers: Kafka服务器地址列表
            request_topic: 请求主题
            response_topic: 响应主题
            response_topic_sora: SORA响应主题
            group_id: 消费者组ID
        """
        # 使用配置参数或默认值
        self.bootstrap_servers = (
            bootstrap_servers or settings.settings.kafka.bootstrap_servers
        )  # Corrected access
        self.request_topic = (
            request_topic or settings.settings.kafka.request_topic
        )  # Corrected access
        self.response_topic = (
            response_topic or settings.settings.kafka.response_topic
        )  # Corrected access
        self.response_topic_sora = (
            response_topic_sora
            or settings.settings.kafka.response_topic_sora  # Corrected access
        )
        self.group_id = group_id or settings.settings.kafka.group_id  # Corrected access

        self.producer = KafkaProducer(
            bootstrap_servers=self.bootstrap_servers,
            value_serializer=lambda x: json.dumps(x).encode("utf-8"),
            request_timeout_ms=30000,
            reconnect_backoff_ms=1000,
            reconnect_backoff_max_ms=10000,
        )

        if init_kafka:
            # 初始化Kafka连接
            self._init_kafka()

        # 初始化3D地图
        # self.map = Map3D(
        #     height=settings.settings.map.height,  # Corrected access
        #     width=settings.settings.map.width,  # Corrected access
        #     depth=settings.settings.map.depth,  # Corrected access
        #     # Removed database arguments as they are no longer needed by Map3D.__init__
        #     # database=settings.settings.database.database, # Corrected access
        #     # user=settings.settings.database.user, # Corrected access
        #     # password=settings.settings.database.password, # Corrected access
        #     # host=settings.settings.database.host, # Corrected access
        #     # port=str(settings.settings.database.port), # Corrected access
        #     # fixed_obstacles and force_reload can be added if needed, using defaults for now
        # )

        converter = create_grid_converter(
            lat_size=settings.settings.map.grid_size.lat,
            lon_size=settings.settings.map.grid_size.lon,
            alt_size=settings.settings.map.grid_size.alt,
            min_lat=settings.settings.map.min_coords.lat,
            min_lon=settings.settings.map.min_coords.lon,
            min_alt=settings.settings.map.min_coords.alt,
        )

        self.map = Map3D(
            settings.settings.map.height,
            settings.settings.map.width,
            settings.settings.map.depth,
            converter,
        )

        # 创建共享的占用图实例
        self.occupancy_map = OccupancyMap(
            self.map.get_height(),
            self.map.get_width(),
            self.map.get_depth(),
            settings.settings.pathplanning.time_buffer,  # 由环境变量控制的时间缓冲 # Corrected access
            5,  # 由环境变量控制的安全包络 # Corrected access
        )

        # 初始化消息处理器
        self._init_handlers()

    def _check_kafka_connection(self, host: str, port: int) -> bool:
        """检查Kafka服务器连接"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)  # 5秒超时
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except:
            return False

    def _init_kafka(self) -> None:
        """初始化Kafka连接"""
        try:
            # 启用详细日志
            logging.getLogger("kafka").setLevel(logging.WARNING)
            logging.getLogger("kafka.conn").setLevel(logging.WARNING)
            logging.getLogger("kafka.consumer").setLevel(logging.WARNING)
            logging.getLogger("kafka.coordinator").setLevel(logging.WARNING)

            # 检查服务器可访问性
            for server in self.bootstrap_servers:
                host, port = server.split(":")
                if not self._check_kafka_connection(host, int(port)):
                    logger.error(f"无法连接到Kafka服务器 {server}")
                    raise KafkaError(f"Kafka服务器 {server} 不可访问")
                else:
                    logger.info(f"Kafka服务器 {server} 可以访问")

            logger.info(f"正在连接Kafka服务器: {self.bootstrap_servers}")
            self.consumer = KafkaConsumer(
                self.request_topic,
                bootstrap_servers=self.bootstrap_servers,
                value_deserializer=lambda x: json.loads(x.decode("utf-8")),
                auto_offset_reset="latest",
                enable_auto_commit=True,
                group_id=self.group_id,
                request_timeout_ms=30000,  # 请求超时时间
                session_timeout_ms=10000,  # 会话超时时间
                security_protocol="PLAINTEXT",  # 安全协议
                api_version_auto_timeout_ms=30000,  # API版本检测超时时间
                reconnect_backoff_ms=1000,  # 重连等待时间
                reconnect_backoff_max_ms=10000,  # 最大重连等待时间
            )

            self.producer = KafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                value_serializer=lambda x: json.dumps(x).encode("utf-8"),
                request_timeout_ms=30000,
                reconnect_backoff_ms=1000,
                reconnect_backoff_max_ms=10000,
            )

            logger.info("Kafka连接初始化成功")
        except Exception as e:
            error_msg = f"Kafka连接初始化失败: {str(e)}"
            logger.error(error_msg)
            raise KafkaError(error_msg)

    def _init_handlers(self) -> None:
        """初始化消息处理器"""
        common_params = {
            "map_3d": self.map,
            "occupancy_map": self.occupancy_map,
            "producer": getattr(self, "producer", None),  # 使用 getattr 安全获取
            "response_topic": self.response_topic,
            "response_topic_sora": self.response_topic_sora,
        }

        # 先创建规划和路由处理器
        planning_handler = PlanningMessageHandler(**common_params)
        route_handler = RouteMessageHandler(**common_params)

        # 创建处理器实例
        self.handlers = {
            "route": route_handler,
            "planning": planning_handler,
            "no_fly_zone": NoFlyZoneMessageHandler(**common_params),
            "approval": ApprovalMessageHandler(
                **common_params,
                planning_handler=planning_handler,
                route_handler=route_handler,
            ),
            "state": StateHandler(**common_params),
            "reroute": RerouteMessageHandler(**common_params),
        }

    def _determine_handler(self, message: Dict) -> str:
        """
        根据消息内容确定使用哪个处理器

        Args:
            message: 消息字典

        Returns:
            str: 处理器类型（'route', 'planning', 'no_fly_zone', 或 'approval'）
        """
        # if "no_fly_zone_name" in message:
        #     return "no_fly_zone"
        if "flow_risk_state" in message:  # 处理审批消息
            logger.info("接收到审批消息")
            return "approval"
        elif message.get("uavrootId") and message.get("uavrootId") != "":  # 固定航线
            return "route"
        return "planning"

    def start_consuming(self) -> None:
        """开始消费消息并处理"""
        logger.info("开始监听路径规划请求...")
        try:
            for message in self.consumer:
                logger.info(
                    f"\n收到消息:\nTopic: {message.topic}\n"
                    f"Partition: {message.partition}\n"
                    f"Offset: {message.offset}\n"
                    f"Key: {message.key}"
                )

                try:
                    request = message.value
                    # 检查是否有plans字段，表示批量规划请求
                    if isinstance(request, list):
                        logger.info("接收到批量规划请求")
                        handler_type = self._determine_handler(request[0])
                        handler = self.handlers[handler_type]

                        # 处理每个规划请求
                        for plan in request:
                            path, error = handler.handle_message(plan)

                            if error:
                                logger.error(
                                    f"处理批量规划请求失败 (flight_id: {plan.get('flight_id')}): {error}"
                                )
                            else:
                                logger.info(
                                    f"成功处理批量规划请求 (flight_id: {plan.get('flight_id')})"
                                )
                    else:
                        # 处理单个请求
                        handler_type = self._determine_handler(request)
                        handler = self.handlers[handler_type]
                        logger.info(f"使用 {handler_type} 处理器处理消息")

                        path, error = handler.handle_message(request)

                        if error:
                            logger.error(f"处理消息失败: {error}")
                        else:
                            logger.info("消息处理成功")

                except Exception as e:
                    logger.error(f"处理消息时出错: {str(e)}")

                logger.info("继续监听消息...")

        except Exception as e:
            logger.error(f"Kafka消费者错误: {str(e)}")
        finally:
            self.close()

    def close(self) -> None:
        """关闭所有连接和资源"""
        try:
            # 1. 通知 MessageHandler 开始关闭
            from .message_handlers.base import MessageHandler

            MessageHandler.is_shutting_down = True

            # 2. 先关闭消费者
            if hasattr(self, "consumer"):
                try:
                    self.consumer.close()
                    logger.info("主消费者已关闭")
                except Exception as e:
                    logger.warning(f"关闭主消费者时出现警告: {str(e)}")

            # 3. 关闭生产者
            if hasattr(self, "producer"):
                try:
                    # 确保所有消息都被发送
                    self.producer.flush()
                    self.producer.close()
                    logger.info("生产者已关闭")
                except Exception as e:
                    logger.warning(f"关闭生产者时出现警告: {str(e)}")

            # 4. 关闭所有处理器
            if hasattr(self, "handlers"):
                for handler_type, handler in self.handlers.items():
                    try:
                        # 如果处理器有自己的关闭方法，调用它
                        if hasattr(handler, "close"):
                            handler.close()
                        logger.info(f"{handler_type} 处理器已关闭")
                    except Exception as e:
                        logger.warning(
                            f"关闭 {handler_type} 处理器时出现警告: {str(e)}"
                        )

            # 5. 确保清理地图和占用图资源
            if hasattr(self, "occupancy_map"):
                try:
                    self.occupancy_map.clear()
                    logger.info("占用图资源已清理")
                except Exception as e:
                    logger.warning(f"清理占用图资源时出现警告: {str(e)}")

            logger.info("所有连接和资源已完全关闭")
        except Exception as e:
            logger.error(f"关闭过程中出现错误: {str(e)}")
        finally:
            # 确保 MessageHandler 的资源也被正确关闭
            try:
                MessageHandler.close_connections()
            except Exception as e:
                logger.warning(f"关闭 MessageHandler 连接时出现警告: {str(e)}")
