from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple
import time
import json
import threading
import random
from datetime import datetime
import paho.mqtt.client as mqtt
import mysql.connector
from ...config import settings  # Changed import again based on user suggestion
from pathfinding_cpp import Map3D, OccupancyMap
from ...core.pathfinding.high_level_policy_3d import CBS3D
from ...utils.logging import get_logger
from ...utils.visualization import FlightPathVisualizer
from ...utils.db_connection_manager import DBConnectionManager

logger = get_logger(__name__)


class MessageHandler(ABC):
    """消息处理器基类"""

    # 类变量，所有实例共享
    map = None
    occupancy_map = None
    grid_converter = None
    producer = None
    uav_topic = None
    response_topic = None
    response_topic_sora = None
    planner = None
    planned_paths = {}  # 存储所有已规划路径
    visualizer = None  # 路径可视化器

    mqtt_client = None  # MQTT客户端实例

    # 数据库连接管理器
    db_manager = None

    # 关闭状态标志
    is_shutting_down = False

    def __init__(
        self,
        map_3d: Map3D,
        occupancy_map: OccupancyMap,
        producer=None,
        response_topic=None,
        response_topic_sora=None,
    ):
        """
        初始化消息处理器

        Args:
            map_3d: 3D地图实例
            occupancy_map: 占用图实例
            producer: Kafka生产者实例
            response_topic: 响应前端主题
            response_topic_sora: SORA响应主题
        """
        # 只在第一个实例初始化时设置类变量
        if MessageHandler.map is None:
            MessageHandler.map = map_3d
            MessageHandler.occupancy_map = occupancy_map
            MessageHandler.grid_converter = map_3d.get_converter()
            MessageHandler.producer = producer
            MessageHandler.uav_topic = (
                settings.settings.kafka.uav_topic
            )  # Corrected access
            MessageHandler.uav_turning_topic = (
                settings.settings.kafka.uav_turning_topic
            )  # Corrected access
            MessageHandler.response_topic = response_topic
            MessageHandler.response_topic_sora = response_topic_sora
            MessageHandler.planner = CBS3D(
                MessageHandler.map, MessageHandler.occupancy_map
            )

            # 初始化可视化器
            # if MessageHandler.visualizer is None:
            #     MessageHandler.visualizer = FlightPathVisualizer(MessageHandler.map)

            # 初始化MQTT客户端(如果启用)
            if (
                settings.settings.mqtt.enabled and MessageHandler.mqtt_client is None
            ):  # Corrected access
                self._init_mqtt_client()

            # 初始化数据库连接管理器（单例模式）
            if MessageHandler.db_manager is None:
                MessageHandler.db_manager = DBConnectionManager()

            # 从数据库加载禁飞区
            self.load_no_fly_zones_from_db()

    def load_no_fly_zones_from_db(self):
        """从数据库加载禁飞区信息"""
        if MessageHandler.map is None or MessageHandler.db_manager is None:
            logger.warning("地图或数据库连接管理器未初始化，无法加载禁飞区")
            return

        logger.info("开始从数据库加载禁飞区信息...")

        def db_operation():
            # 获取数据库连接
            conn = MessageHandler.db_manager.get_connection()
            if conn is None:
                logger.error("无法获取数据库连接，禁飞区加载失败")
                return False

            try:
                with conn.cursor() as cursor:
                    # 查询fly_zone表中的所有禁飞区
                    cursor.execute("SELECT name, shape, path FROM fly_zone")
                    no_fly_zones = cursor.fetchall()

                    if not no_fly_zones:
                        logger.info("数据库中没有找到禁飞区信息")
                        return True

                    logger.info(f"从数据库中找到 {len(no_fly_zones)} 个禁飞区")

                    # 处理每个禁飞区
                    for zone in no_fly_zones:
                        name, shape, path_str = zone
                        try:
                            # 解析路径数据
                            path_data = json.loads(path_str)

                            if shape.lower() == "circle":
                                # 圆形禁飞区: [{"lng":117.21909490569156,"lat":31.841424362849992},1500]
                                if (
                                    len(path_data) == 2
                                    and isinstance(path_data[0], dict)
                                    and isinstance(path_data[1], (int, float))
                                ):
                                    center = path_data[0]
                                    radius = path_data[1]

                                    # 添加圆形禁飞区
                                    _, conflicts = (
                                        MessageHandler.map.add_solid_cylindrical_no_fly_zone(
                                            center_lat_deg=center["lat"],
                                            center_lon_deg=center["lng"],
                                            radius_meters=radius,
                                            zone_name=name,
                                            buffer_distance_meters=settings.settings.map.buffer_distance_meters,
                                            planned_paths_dict=MessageHandler.planned_paths,
                                            boundary_thickness_grids=settings.settings.map.boundary_thickness,
                                        )
                                    )

                                    if conflicts:
                                        logger.warning(
                                            f"禁飞区 {name} 与 {len(conflicts)} 个已规划路径冲突"
                                        )
                                    else:
                                        logger.info(
                                            f"成功加载圆形禁飞区: {name}, 中心点: ({center['lat']}, {center['lng']}), 半径: {radius}米"
                                        )
                                else:
                                    logger.error(
                                        f"圆形禁飞区 {name} 的路径数据格式错误: {path_str}"
                                    )

                            elif shape.lower() == "polygon":
                                # 多边形禁飞区: [{"lng":117.36768466639249,"lat":31.96450702079392},...]
                                if (
                                    isinstance(path_data, list)
                                    and len(path_data) >= 3
                                    and all(isinstance(p, dict) for p in path_data)
                                ):
                                    # 转换path_data格式：从[{"lng":..., "lat":...}, ...] 转换为 [(lat, lon, alt), ...]
                                    path_data = [
                                        (point["lat"], point["lng"], 0)
                                        for point in path_data
                                    ]
                                    # 添加多边形禁飞区
                                    _, conflicts = (
                                        MessageHandler.map.add_hollow_polygonal_no_fly_zone(
                                            polygon_vertices=path_data,
                                            zone_name=name,
                                            offset_meters=settings.settings.map.buffer_distance_meters,
                                            boundary_thickness_grids=settings.settings.map.boundary_thickness,
                                            planned_paths_dict=MessageHandler.planned_paths,
                                        )
                                    )

                                    if conflicts:
                                        logger.warning(
                                            f"禁飞区 {name} 与 {len(conflicts)} 个已规划路径冲突"
                                        )
                                    else:
                                        logger.info(
                                            f"成功加载多边形禁飞区: {name}, 顶点数: {len(path_data)}"
                                        )
                                else:
                                    logger.error(
                                        f"多边形禁飞区 {name} 的路径数据格式错误: {path_str}"
                                    )

                            else:
                                logger.warning(
                                    f"未知的禁飞区类型: {shape}, 名称: {name}"
                                )

                        except json.JSONDecodeError:
                            logger.error(
                                f"禁飞区 {name} 的路径数据不是有效的JSON: {path_str}"
                            )
                        except Exception as e:
                            logger.error(f"处理禁飞区 {name} 时出错: {str(e)}")

                return True

            except Exception as e:
                logger.error(f"查询禁飞区数据时出错: {str(e)}")
                return False

        try:
            # 执行数据库操作
            success = self._execute_with_retry(db_operation)
            if success:
                logger.info("禁飞区加载完成")
            else:
                logger.error("禁飞区加载失败")
        except Exception as e:
            logger.error(f"加载禁飞区时出错: {str(e)}")

    def _init_db_conn(self):  # Corrected indentation
        """初始化数据库连接"""
        thread_id = threading.get_ident()
        logger.debug(
            f"[Thread-{thread_id}] Attempting to initialize DB connection. Current db_conn is None: {MessageHandler.db_conn is None}"
        )

        # 如果已经有连接，先检查连接是否有效
        if MessageHandler.db_conn is not None:
            try:
                # 尝试执行简单查询测试连接
                with MessageHandler.db_conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                # 连接有效，更新最后活动时间并返回
                MessageHandler.db_conn_last_active = time.time()
                logger.debug(
                    f"[Thread-{thread_id}] Existing DB connection (ID: {id(MessageHandler.db_conn)}) is valid, skipping initialization."
                )
                return
            except Exception as test_error:
                # 连接无效，记录错误并继续初始化新连接
                logger.warning(
                    f"[Thread-{thread_id}] Existing DB connection (ID: {id(MessageHandler.db_conn)}) failed test: {str(test_error)}"
                )
                try:
                    # 尝试关闭失效的连接
                    MessageHandler.db_conn.close()
                except Exception:
                    pass  # 忽略关闭错误
                # 设置为None以便重新初始化
                MessageHandler.db_conn = None

        # 重试机制
        max_retries = 3
        retry_count = 0
        backoff_time = 1  # 初始等待时间（秒）

        while retry_count < max_retries:
            try:
                # 创建连接配置字典 - 完全禁用连接池
                conn_config = {
                    "host": settings.settings.database.host,
                    "port": settings.settings.database.port,
                    "user": settings.settings.database.user,
                    "password": settings.settings.database.password,
                    "database": settings.settings.database.database,
                    "use_pure": True,
                    "autocommit": True,
                    "buffered": True,
                    "get_warnings": True,
                    "raise_on_warnings": True,
                    "connection_timeout": 30,  # 减小连接超时时间
                    "connect_timeout": 15,  # 减小连接超时时间
                    "ssl_disabled": True,  # 禁用SSL
                }

                # 日志记录配置（隐藏密码）
                log_config = conn_config.copy()
                log_config["password"] = "********"
                logger.debug(
                    f"[Thread-{thread_id}] Creating new DB connection with config: {log_config}, retry {retry_count+1}/{max_retries}"
                )

                try:
                    # 创建数据库连接，添加重试逻辑
                    connection_retry = 0
                    max_connection_retries = 3
                    connection_retry_delay = 1  # 初始重试延迟（秒）

                    while connection_retry < max_connection_retries:
                        try:
                            # 创建数据库连接
                            new_conn = mysql.connector.connect(**conn_config)

                            # 测试新连接
                            with new_conn.cursor() as cursor:
                                cursor.execute("SELECT 1")
                                cursor.fetchone()

                            # 连接测试成功，更新全局连接对象
                            MessageHandler.db_conn = new_conn
                            MessageHandler.db_conn_last_active = time.time()
                            logger.debug(
                                f"[Thread-{thread_id}] DB connection initialized successfully. db_conn object ID: {id(MessageHandler.db_conn)}"
                            )
                            # 成功创建连接，跳出重试循环
                            break

                        except (
                            mysql.connector.errors.OperationalError,
                            mysql.connector.errors.InterfaceError,
                        ) as conn_error:
                            # 处理连接错误（如网络问题）
                            connection_retry += 1
                            error_msg = str(conn_error)

                            if connection_retry < max_connection_retries:
                                logger.warning(
                                    f"[Thread-{thread_id}] 连接错误 (尝试 {connection_retry}/{max_connection_retries}): {error_msg}. 等待 {connection_retry_delay} 秒后重试..."
                                )
                                time.sleep(connection_retry_delay)
                                # 指数退避，增加重试延迟
                                connection_retry_delay *= 2
                            else:
                                logger.error(
                                    f"[Thread-{thread_id}] 连接错误，已重试 {max_connection_retries} 次仍然失败: {error_msg}"
                                )
                                # 重新抛出最后一个异常
                                raise

                except mysql.connector.errors.DatabaseError as db_error:
                    # 特别处理"Too many connections"错误
                    if "Too many connections" in str(db_error):
                        logger.critical(
                            f"[Thread-{thread_id}] 数据库连接数已达上限: {str(db_error)}"
                        )
                        # 尝试强制清理所有连接池
                        self._handle_too_many_connections()
                        # 重新抛出异常，让外层重试机制处理
                        raise
                    else:
                        # 其他数据库错误，直接重新抛出
                        raise

                # 初始化成功，退出循环
                break

            except Exception as e:
                retry_count += 1
                logger.error(
                    f"[Thread-{thread_id}] DB connection initialization failed (attempt {retry_count}/{max_retries}): {str(e)}",
                    exc_info=True,
                )

                # 如果还有重试机会，等待后重试
                if retry_count < max_retries:
                    logger.warning(
                        f"[Thread-{thread_id}] Retrying in {backoff_time} seconds..."
                    )
                    time.sleep(backoff_time)
                    # 指数退避，增加等待时间
                    backoff_time *= 2
                else:
                    # 所有重试都失败，确保连接为None
                    logger.critical(
                        f"[Thread-{thread_id}] All DB connection initialization attempts failed after {max_retries} retries."
                    )
                    MessageHandler.db_conn = None

    def _check_db_conn(self):
        """检查并维护数据库连接 - 使用数据库连接管理器"""
        # 确保数据库连接管理器已初始化
        if MessageHandler.db_manager is None:
            MessageHandler.db_manager = DBConnectionManager()

        # 委托给连接管理器检查连接
        return MessageHandler.db_manager._check_db_conn()

    def _handle_too_many_connections(self):
        """处理数据库连接数过多的情况"""
        thread_id = threading.get_ident()
        logger.critical(f"[Thread-{thread_id}] 执行紧急连接清理，尝试释放所有连接")

        # 1. 关闭当前连接
        if MessageHandler.db_conn is not None:
            try:
                logger.info(
                    f"[Thread-{thread_id}] 关闭当前连接 (ID: {id(MessageHandler.db_conn)})"
                )
                MessageHandler.db_conn.close()
            except Exception as e:
                logger.error(f"[Thread-{thread_id}] 关闭当前连接失败: {str(e)}")
            finally:
                MessageHandler.db_conn = None

        # 2. 尝试清理MySQL连接池
        try:
            # 获取所有连接池
            if hasattr(mysql.connector.pooling, "_CONNECTION_POOLS"):
                pools = mysql.connector.pooling._CONNECTION_POOLS
                logger.info(f"[Thread-{thread_id}] 发现 {len(pools)} 个连接池")

                # 遍历所有连接池并尝试关闭
                for pool_name, pool in list(pools.items()):
                    try:
                        logger.info(f"[Thread-{thread_id}] 尝试清理连接池: {pool_name}")
                        # 关闭池中的所有连接
                        if hasattr(pool, "_queue_free"):
                            free_conns = list(pool._queue_free)
                            for conn in free_conns:
                                try:
                                    if conn and hasattr(conn, "close"):
                                        conn.close()
                                except:
                                    pass

                        # 从全局池字典中移除
                        mysql.connector.pooling._CONNECTION_POOLS.pop(pool_name, None)
                        logger.info(f"[Thread-{thread_id}] 成功清理连接池: {pool_name}")
                    except Exception as pool_error:
                        logger.error(
                            f"[Thread-{thread_id}] 清理连接池 {pool_name} 失败: {str(pool_error)}"
                        )
        except Exception as e:
            logger.error(f"[Thread-{thread_id}] 清理连接池失败: {str(e)}")

        # 3. 等待一段时间，让数据库有时间释放连接
        wait_time = 3  # 等待3秒
        logger.info(f"[Thread-{thread_id}] 等待 {wait_time} 秒让数据库释放连接")
        time.sleep(wait_time)

        # 4. 尝试使用最小配置创建一个新连接（不使用连接池）
        try:
            # 尝试关闭所有已知的MySQL连接
            # 这是一个更激进的方法，尝试释放所有连接
            try:
                # 获取MySQL连接器中的所有连接池
                if hasattr(mysql.connector.pooling, "_CONNECTION_POOLS"):
                    for pool_name in list(
                        mysql.connector.pooling._CONNECTION_POOLS.keys()
                    ):
                        try:
                            del mysql.connector.pooling._CONNECTION_POOLS[pool_name]
                            logger.critical(
                                f"[Thread-{thread_id}] 强制删除连接池: {pool_name}"
                            )
                        except:
                            pass

                    # 尝试完全清空连接池字典
                    try:
                        mysql.connector.pooling._CONNECTION_POOLS.clear()
                        logger.critical(f"[Thread-{thread_id}] 已清空所有连接池字典")
                    except:
                        pass

                # 尝试使用gc模块查找和关闭所有MySQL连接对象
                import gc

                for obj in gc.get_objects():
                    if isinstance(obj, mysql.connector.connection.MySQLConnection):
                        try:
                            if (
                                hasattr(obj, "close")
                                and obj is not MessageHandler.db_conn
                            ):
                                obj.close()
                                logger.critical(
                                    f"[Thread-{thread_id}] 强制关闭找到的MySQL连接对象: {id(obj)}"
                                )
                        except:
                            pass
            except Exception as gc_error:
                logger.error(
                    f"[Thread-{thread_id}] 尝试强制清理连接时出错: {str(gc_error)}"
                )

            # 等待更长时间让连接释放
            wait_time = 5  # 增加等待时间
            logger.info(f"[Thread-{thread_id}] 等待 {wait_time} 秒让数据库释放连接")
            time.sleep(wait_time)

            # 使用最小配置创建连接
            minimal_config = {
                "host": settings.settings.database.host,
                "port": settings.settings.database.port,
                "user": settings.settings.database.user,
                "password": settings.settings.database.password,
                "database": settings.settings.database.database,
                "use_pure": True,
                "connect_timeout": 10,
                "connection_timeout": 10,
                "ssl_disabled": True,
            }

            logger.info(f"[Thread-{thread_id}] 尝试创建紧急连接")
            emergency_conn = mysql.connector.connect(**minimal_config)

            # 测试连接
            with emergency_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()

            # 关闭紧急连接
            emergency_conn.close()
            logger.info(f"[Thread-{thread_id}] 紧急连接测试成功并已关闭")
        except Exception as e:
            logger.error(f"[Thread-{thread_id}] 创建紧急连接失败: {str(e)}")

        logger.info(f"[Thread-{thread_id}] 紧急连接清理完成")

    def _monitor_pool_usage(self):
        """监控连接池使用情况"""
        thread_id = threading.get_ident()

        if MessageHandler.db_conn is None:
            logger.debug(f"[Thread-{thread_id}] 数据库连接为None，无法监控连接池")
            return 0

        try:
            # 尝试获取连接池信息
            if hasattr(MessageHandler.db_conn, "_cnx_pool"):
                pool = MessageHandler.db_conn._cnx_pool
                if pool and hasattr(pool, "_queue_free"):
                    # 获取空闲连接数量
                    free_connections = len(pool._queue_free)
                    total_connections = pool._pool_size
                    used_connections = total_connections - free_connections
                    usage_percent = (used_connections / total_connections) * 100

                    # 根据使用率决定日志级别
                    if usage_percent > 90:
                        logger.warning(
                            f"[Thread-{thread_id}] 连接池使用率过高: {used_connections}/{total_connections} ({usage_percent:.1f}%)"
                        )
                    elif usage_percent > 70:
                        logger.info(
                            f"[Thread-{thread_id}] 连接池使用率较高: {used_connections}/{total_connections} ({usage_percent:.1f}%)"
                        )
                    else:
                        logger.debug(
                            f"[Thread-{thread_id}] 连接池使用情况: {used_connections}/{total_connections} ({usage_percent:.1f}%)"
                        )

                    # 返回使用百分比
                    return usage_percent

                # 如果没有_queue_free属性，尝试获取其他信息
                pool_info = {
                    "pool_name": getattr(pool, "pool_name", "unknown"),
                    "pool_size": getattr(pool, "_pool_size", 0),
                    "pool_max_size": getattr(pool, "_max_pool_size", 0),
                }
                logger.debug(
                    f"[Thread-{thread_id}] 连接池信息(无法获取使用率): {pool_info}"
                )
                return 50  # 返回默认值50%，触发中等级别的清理
            else:
                logger.debug(f"[Thread-{thread_id}] 数据库连接没有连接池属性")
        except Exception as e:
            logger.warning(f"[Thread-{thread_id}] 监控连接池使用情况失败: {str(e)}")

        return 0  # 默认返回0

    def _cleanup_db_pool(self):
        """清理数据库连接池中的空闲连接"""
        thread_id = threading.get_ident()

        if MessageHandler.db_conn is None:
            logger.info(f"[Thread-{thread_id}] 数据库连接为None，尝试重新初始化")
            self._init_db_conn()
            return

        try:
            # 先检查连接池使用情况
            usage_percent = self._monitor_pool_usage()

            # 如果使用率超过60%，则强制重新初始化连接池（降低阈值，更积极地清理）
            if usage_percent > 60:
                logger.warning(
                    f"[Thread-{thread_id}] 连接池使用率过高 ({usage_percent:.1f}%)，强制重新初始化"
                )

            # 保存当前连接对象ID用于日志
            current_conn_id = (
                id(MessageHandler.db_conn)
                if MessageHandler.db_conn is not None
                else "None"
            )

            # 尝试释放连接并重新初始化
            logger.debug(
                f"[Thread-{thread_id}] 尝试清理数据库连接池，当前连接ID: {current_conn_id}"
            )

            # 先关闭当前连接
            try:
                if MessageHandler.db_conn is not None:
                    # 尝试获取连接池信息用于日志
                    pool_info = "未知"
                    if hasattr(MessageHandler.db_conn, "_cnx_pool"):
                        pool = MessageHandler.db_conn._cnx_pool
                        if pool:
                            pool_info = {
                                "pool_name": getattr(pool, "pool_name", "unknown"),
                                "pool_size": getattr(pool, "_pool_size", 0),
                            }

                    logger.debug(
                        f"[Thread-{thread_id}] 关闭连接前的连接池信息: {pool_info}"
                    )

                    # 关闭连接
                    MessageHandler.db_conn.close()
                    logger.debug(
                        f"[Thread-{thread_id}] 成功关闭数据库连接 ID: {current_conn_id}"
                    )
            except Exception as close_error:
                logger.warning(
                    f"[Thread-{thread_id}] 关闭数据库连接失败: {str(close_error)}"
                )
            finally:
                # 无论关闭是否成功，都将连接设置为None并重新初始化
                MessageHandler.db_conn = None

            # 等待短暂停止后重新初始化连接
            time.sleep(0.5)
            logger.debug(f"[Thread-{thread_id}] 开始重新初始化数据库连接")
            self._init_db_conn()

            if MessageHandler.db_conn:
                new_conn_id = id(MessageHandler.db_conn)
                logger.debug(
                    f"[Thread-{thread_id}] 数据库连接池重新初始化成功，新连接ID: {new_conn_id}"
                )

                # 测试新连接
                try:
                    with MessageHandler.db_conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                    logger.debug(f"[Thread-{thread_id}] 新连接测试成功")
                except Exception as test_error:
                    logger.error(
                        f"[Thread-{thread_id}] 新连接测试失败: {str(test_error)}"
                    )
                    # 如果测试失败，再次设置为None
                    MessageHandler.db_conn = None
                    return False

                return True
            else:
                logger.warning(f"[Thread-{thread_id}] 数据库连接池重新初始化失败")
                return False
        except Exception as e:
            logger.error(
                f"[Thread-{thread_id}] 清理数据库连接池失败: {str(e)}", exc_info=True
            )
            # 确保连接被设置为None以便下次尝试重新初始化
            MessageHandler.db_conn = None
            return False

    # 添加一个新的定时器属性用于连接池清理
    db_pool_cleanup_timer = None
    db_pool_cleanup_interval = 10  # 每10秒清理一次连接池

    # 添加连接保活定时器
    db_conn_keepalive_timer = None
    db_conn_keepalive_interval = 60  # 每60秒执行一次保活操作

    def _start_db_pool_cleanup_timer(self):
        """启动连接池定期清理定时器"""
        thread_id = threading.get_ident()

        if MessageHandler.db_pool_cleanup_timer is not None:
            logger.debug(f"[Thread-{thread_id}] 连接池清理定时器已经启动，跳过")
            return  # 定时器已经启动

        def periodic_cleanup():
            """定期清理连接池"""
            cleanup_thread_id = threading.get_ident()
            try:
                logger.debug(f"[Thread-{cleanup_thread_id}] 执行定期连接池清理检查")

                # 检查连接是否为None
                if MessageHandler.db_conn is None:
                    logger.warning(
                        f"[Thread-{cleanup_thread_id}] 数据库连接为None，尝试重新初始化"
                    )
                    self._init_db_conn()
                    # 如果初始化成功，跳过本次清理
                    if MessageHandler.db_conn is not None:
                        logger.info(
                            f"[Thread-{cleanup_thread_id}] 数据库连接重新初始化成功，跳过本次清理"
                        )
                    else:
                        logger.error(
                            f"[Thread-{cleanup_thread_id}] 数据库连接重新初始化失败"
                        )
                    # 无论成功与否，都继续执行，以便重新设置定时器

                # 监控连接池使用情况
                usage_percent = self._monitor_pool_usage()

                # 根据使用率决定清理策略 - 更积极地清理
                if usage_percent > 50:  # 进一步降低阈值，更积极地清理
                    logger.warning(
                        f"[Thread-{cleanup_thread_id}] 连接池使用率过高 ({usage_percent:.1f}%)，执行强制清理"
                    )
                    self._cleanup_db_pool()
                elif usage_percent > 30:  # 进一步降低阈值，更积极地清理
                    logger.debug(
                        f"[Thread-{cleanup_thread_id}] 连接池使用率较高 ({usage_percent:.1f}%)，执行常规清理"
                    )
                    self._cleanup_db_pool()
                elif random.random() < 0.5:  # 增加到50%的概率执行清理
                    logger.debug(f"[Thread-{cleanup_thread_id}] 执行随机连接池清理")
                    self._cleanup_db_pool()
                else:
                    # 即使不清理，也尝试关闭并重新初始化连接或执行连接检查
                    if random.random() < 0.3:  # 30%的概率执行轻量级清理
                        logger.debug(f"[Thread-{cleanup_thread_id}] 执行轻量级连接重置")
                        try:
                            if MessageHandler.db_conn is not None:
                                try:
                                    MessageHandler.db_conn.close()
                                except:
                                    pass
                                MessageHandler.db_conn = None
                                self._init_db_conn()
                        except Exception as e:
                            logger.warning(
                                f"[Thread-{cleanup_thread_id}] 轻量级连接重置失败: {str(e)}"
                            )
                    else:
                        # 执行常规连接检查
                        logger.debug(f"[Thread-{cleanup_thread_id}] 执行常规连接检查")
                        self._check_db_conn()
            except Exception as e:
                logger.error(
                    f"[Thread-{cleanup_thread_id}] 定期连接池清理失败: {str(e)}",
                    exc_info=True,
                )
                # 如果清理过程中出错，尝试重新初始化连接
                try:
                    if MessageHandler.db_conn is not None:
                        try:
                            MessageHandler.db_conn.close()
                        except:
                            pass
                    MessageHandler.db_conn = None
                    logger.warning(
                        f"[Thread-{cleanup_thread_id}] 清理失败，强制重置连接并尝试重新初始化"
                    )
                    self._init_db_conn()
                except Exception as init_error:
                    logger.error(
                        f"[Thread-{cleanup_thread_id}] 重新初始化连接失败: {str(init_error)}"
                    )
            finally:
                # 重新设置定时器
                try:
                    # 确保usage_percent已定义，如果未定义则使用默认值0
                    current_usage = getattr(locals(), "usage_percent", 0)

                    # 使用动态间隔，根据连接池使用情况调整
                    if current_usage > 80:
                        # 高使用率时，更频繁地清理
                        cleanup_interval = max(
                            3, MessageHandler.db_pool_cleanup_interval // 3
                        )
                    elif current_usage > 60:
                        # 中等使用率
                        cleanup_interval = max(
                            5, MessageHandler.db_pool_cleanup_interval // 2
                        )
                    else:
                        # 低使用率，使用正常间隔
                        cleanup_interval = MessageHandler.db_pool_cleanup_interval

                    logger.debug(
                        f"[Thread-{cleanup_thread_id}] 设置下一次清理间隔为 {cleanup_interval} 秒"
                    )

                    MessageHandler.db_pool_cleanup_timer = threading.Timer(
                        cleanup_interval, periodic_cleanup
                    )
                    MessageHandler.db_pool_cleanup_timer.daemon = True
                    MessageHandler.db_pool_cleanup_timer.start()
                except Exception as timer_error:
                    logger.error(
                        f"[Thread-{cleanup_thread_id}] 设置清理定时器失败: {str(timer_error)}"
                    )
                    # 尝试使用固定间隔重新设置
                    try:
                        MessageHandler.db_pool_cleanup_timer = threading.Timer(
                            10, periodic_cleanup  # 固定使用10秒作为备用间隔
                        )
                        MessageHandler.db_pool_cleanup_timer.daemon = True
                        MessageHandler.db_pool_cleanup_timer.start()
                    except:
                        logger.critical(
                            f"[Thread-{cleanup_thread_id}] 无法重新设置清理定时器，连接池清理将停止"
                        )

        # 启动第一个定时器
        try:
            MessageHandler.db_pool_cleanup_timer = threading.Timer(
                MessageHandler.db_pool_cleanup_interval, periodic_cleanup
            )
            MessageHandler.db_pool_cleanup_timer.daemon = True
            MessageHandler.db_pool_cleanup_timer.start()
            logger.debug(
                f"[Thread-{thread_id}] 已启动连接池定期清理，初始间隔: {MessageHandler.db_pool_cleanup_interval}秒"
            )
        except Exception as e:
            logger.error(f"[Thread-{thread_id}] 启动连接池清理定时器失败: {str(e)}")
            # 尝试使用备用方法启动
            try:
                MessageHandler.db_pool_cleanup_timer = threading.Timer(
                    10, periodic_cleanup  # 固定使用10秒作为备用间隔
                )
                MessageHandler.db_pool_cleanup_timer.daemon = True
                MessageHandler.db_pool_cleanup_timer.start()
                logger.debug(
                    f"[Thread-{thread_id}] 使用备用方法启动连接池清理定时器，间隔: 10秒"
                )
            except Exception as backup_error:
                logger.critical(
                    f"[Thread-{thread_id}] 备用方法启动连接池清理定时器也失败: {str(backup_error)}"
                )

    def _keepalive_db_conn(self):
        """执行数据库连接保活操作"""
        thread_id = threading.get_ident()

        if MessageHandler.db_conn is None:
            logger.debug(f"[Thread-{thread_id}] 数据库连接为None，无法执行保活操作")
            return

        try:
            # 执行一个简单的查询来保持连接活跃
            with MessageHandler.db_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            logger.debug(f"[Thread-{thread_id}] 数据库连接保活操作成功")

            # 更新最后活动时间
            MessageHandler.db_conn_last_active = time.time()
        except Exception as e:
            logger.warning(f"[Thread-{thread_id}] 数据库连接保活操作失败: {str(e)}")
            # 连接可能已断开，尝试重新初始化
            try:
                if MessageHandler.db_conn is not None:
                    try:
                        MessageHandler.db_conn.close()
                    except:
                        pass
                MessageHandler.db_conn = None
                self._init_db_conn()
                logger.info(f"[Thread-{thread_id}] 保活失败后重新初始化连接成功")
            except Exception as init_error:
                logger.error(
                    f"[Thread-{thread_id}] 保活失败后重新初始化连接失败: {str(init_error)}"
                )

    def _start_db_conn_keepalive_timer(self):
        """启动数据库连接保活定时器"""
        thread_id = threading.get_ident()

        if MessageHandler.db_conn_keepalive_timer is not None:
            logger.debug(f"[Thread-{thread_id}] 数据库连接保活定时器已经启动，跳过")
            return  # 定时器已经启动

        def periodic_keepalive():
            """定期执行连接保活操作"""
            keepalive_thread_id = threading.get_ident()
            try:
                logger.debug(
                    f"[Thread-{keepalive_thread_id}] 执行定期数据库连接保活操作"
                )
                self._keepalive_db_conn()
            except Exception as e:
                logger.error(
                    f"[Thread-{keepalive_thread_id}] 定期数据库连接保活操作失败: {str(e)}"
                )
            finally:
                # 重新设置定时器
                MessageHandler.db_conn_keepalive_timer = threading.Timer(
                    MessageHandler.db_conn_keepalive_interval, periodic_keepalive
                )
                MessageHandler.db_conn_keepalive_timer.daemon = True
                MessageHandler.db_conn_keepalive_timer.start()

        # 启动第一个定时器
        try:
            MessageHandler.db_conn_keepalive_timer = threading.Timer(
                MessageHandler.db_conn_keepalive_interval, periodic_keepalive
            )
            MessageHandler.db_conn_keepalive_timer.daemon = True
            MessageHandler.db_conn_keepalive_timer.start()
            logger.debug(
                f"[Thread-{thread_id}] 已启动数据库连接保活定时器，间隔: {MessageHandler.db_conn_keepalive_interval}秒"
            )
        except Exception as e:
            logger.error(f"[Thread-{thread_id}] 启动数据库连接保活定时器失败: {str(e)}")

    def _start_db_conn_check_timer(self):
        """启动数据库连接定期检查定时器"""
        thread_id = threading.get_ident()

        if MessageHandler.db_conn_check_timer is not None:
            logger.debug(f"[Thread-{thread_id}] 数据库连接检查定时器已经启动，跳过")
            return  # 定时器已经启动

        def periodic_check():
            """定期检查数据库连接"""
            check_thread_id = threading.get_ident()
            try:
                logger.debug(f"[Thread-{check_thread_id}] 执行定期数据库连接检查")

                # 检查连接状态
                connection_status = self._check_db_conn()

                # 如果连接检查失败，尝试更积极的恢复措施
                if not connection_status:
                    logger.warning(
                        f"[Thread-{check_thread_id}] 连接检查失败，尝试强制重新初始化"
                    )
                    # 强制关闭并重新初始化
                    if MessageHandler.db_conn is not None:
                        try:
                            MessageHandler.db_conn.close()
                        except:
                            pass
                    MessageHandler.db_conn = None
                    self._init_db_conn()

                    # 再次检查连接状态
                    if MessageHandler.db_conn is None:
                        logger.error(
                            f"[Thread-{check_thread_id}] 强制重新初始化后连接仍然失败"
                        )
                    else:
                        logger.info(f"[Thread-{check_thread_id}] 强制重新初始化成功")

                # 监控连接池使用情况
                usage_percent = self._monitor_pool_usage()

                # 根据使用率决定是否需要立即清理 - 更积极地清理
                if usage_percent > 50:  # 降低阈值，更积极地清理
                    logger.warning(
                        f"[Thread-{check_thread_id}] 连接池使用率过高 ({usage_percent:.1f}%)，立即执行清理"
                    )
                    self._cleanup_db_pool()
                elif usage_percent > 30:  # 中等使用率也执行清理
                    logger.debug(
                        f"[Thread-{check_thread_id}] 连接池使用率中等 ({usage_percent:.1f}%)，执行预防性清理"
                    )
                    # 尝试关闭并重新初始化连接
                    try:
                        if MessageHandler.db_conn is not None:
                            try:
                                MessageHandler.db_conn.close()
                            except:
                                pass
                            MessageHandler.db_conn = None
                            self._init_db_conn()
                            logger.debug(
                                f"[Thread-{check_thread_id}] 预防性连接重置成功"
                            )
                    except Exception as e:
                        logger.warning(
                            f"[Thread-{check_thread_id}] 预防性连接重置失败: {str(e)}"
                        )

                # 确保连接池清理定时器已启动
                if MessageHandler.db_pool_cleanup_timer is None:
                    logger.warning(
                        f"[Thread-{check_thread_id}] 连接池清理定时器未启动，重新启动"
                    )
                    self._start_db_pool_cleanup_timer()

                # 重新设置定时器
                MessageHandler.db_conn_check_timer = threading.Timer(
                    MessageHandler.db_conn_check_interval, periodic_check
                )
                MessageHandler.db_conn_check_timer.daemon = True  # 设为守护线程
                MessageHandler.db_conn_check_timer.start()
                logger.debug(
                    f"[Thread-{check_thread_id}] 已重新设置数据库连接检查定时器，间隔: {MessageHandler.db_conn_check_interval}秒"
                )
            except Exception as e:
                logger.error(
                    f"[Thread-{check_thread_id}] 定期数据库连接检查失败: {str(e)}",
                    exc_info=True,
                )

                # 即使出错，也尝试重新设置定时器
                try:
                    MessageHandler.db_conn_check_timer = threading.Timer(
                        max(15, MessageHandler.db_conn_check_interval),
                        periodic_check,  # 出错后使用较长间隔
                    )
                    MessageHandler.db_conn_check_timer.daemon = True
                    MessageHandler.db_conn_check_timer.start()
                    logger.debug(
                        f"[Thread-{check_thread_id}] 连接检查出错后重新设置定时器，间隔: {max(15, MessageHandler.db_conn_check_interval)}秒"
                    )
                except Exception as timer_error:
                    logger.critical(
                        f"[Thread-{check_thread_id}] 无法重新设置连接检查定时器: {str(timer_error)}"
                    )

        # 启动第一个定时器
        try:
            MessageHandler.db_conn_check_timer = threading.Timer(
                MessageHandler.db_conn_check_interval, periodic_check
            )
            MessageHandler.db_conn_check_timer.daemon = True  # 设为守护线程
            MessageHandler.db_conn_check_timer.start()
            logger.debug(
                f"[Thread-{thread_id}] 已启动数据库连接定期检查，间隔: {MessageHandler.db_conn_check_interval}秒"
            )
        except Exception as e:
            logger.error(f"[Thread-{thread_id}] 启动数据库连接检查定时器失败: {str(e)}")
            # 尝试使用备用方法启动
            try:
                MessageHandler.db_conn_check_timer = threading.Timer(
                    30, periodic_check  # 使用较长的备用间隔
                )
                MessageHandler.db_conn_check_timer.daemon = True
                MessageHandler.db_conn_check_timer.start()
                logger.debug(
                    f"[Thread-{thread_id}] 使用备用方法启动数据库连接检查定时器，间隔: 30秒"
                )
            except:
                logger.critical(
                    f"[Thread-{thread_id}] 备用方法启动数据库连接检查定时器也失败"
                )

        # 同时启动连接池清理定时器
        self._start_db_pool_cleanup_timer()

    @abstractmethod
    def handle_message(self, message: Dict) -> Tuple[Optional[List], Optional[str]]:
        """
        处理消息的抽象方法

        Args:
            message: 消息字典

        Returns:
            Tuple[Optional[List], Optional[str]]: (路径点列表, 错误信息)
        """
        pass

    def parse_time(self, time_str: str) -> int:
        """
        将时间字符串转换为Unix时间戳

        Args:
            time_str: 格式为 "YYYY-MM-DD HH:MM:SS" 的时间字符串

        Returns:
            int: Unix时间戳（秒）
        """
        dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        return int(dt.timestamp())

    def parse_point(self, point_str: str) -> Dict:
        """
        解析坐标字符串为经纬度和高度

        Args:
            point_str: 格式为 "lon,lat,alt" 的坐标字符串

        Returns:
            Dict: 包含经纬度和高度的字典
        """
        lon, lat, alt = map(float, point_str.split(","))
        return {"lat": lat, "lon": lon, "alt": alt}

    def store_path(
        self,
        flight_id: str,
        path_nodes: List,
        turn_path_nodes: List,
        request_info: Dict,
    ) -> None:
        """
        存储已规划的路径

        Args:
            flight_id: 航班ID
            path_nodes: 完整node路径
            turn_path_nodes: 拐点node路径
            request_info: 请求相关信息
        """
        if request_info is None:
            full_info = MessageHandler.planned_paths[flight_id].copy()
        else:
            full_info = request_info.copy()

        full_info["path_nodes"] = path_nodes  # 完整node路径
        full_info["turn_path_nodes"] = turn_path_nodes  # 拐点node路径
        MessageHandler.planned_paths[flight_id] = full_info

        # 将路径添加到可视化器
        # if MessageHandler.visualizer:
        #     MessageHandler.visualizer.add_path(flight_id, grid_coords)

    def visualize_paths(self):
        """可视化所有已规划的路径和禁飞区"""
        if MessageHandler.visualizer:
            MessageHandler.visualizer.show()

    def _init_mqtt_client(self):
        """初始化MQTT客户端"""
        try:
            MessageHandler.mqtt_client = mqtt.Client()
            MessageHandler.mqtt_client.username_pw_set(
                settings.settings.mqtt.username,
                settings.settings.mqtt.password,  # Corrected access
            )
            # 解析broker地址和端口
            host, port = settings.settings.mqtt.broker.split(":")  # Corrected access
            MessageHandler.mqtt_client.connect(host, int(port), 60)
            MessageHandler.mqtt_client.loop_start()
            logger.info("MQTT客户端初始化成功")
        except Exception as e:
            logger.error(f"MQTT客户端初始化失败: {str(e)}")
            MessageHandler.mqtt_client = None

    def _send_mqtt(self, topic: str, message: Dict) -> None:
        """通过MQTT发送消息"""
        if not MessageHandler.mqtt_client:
            logger.error("MQTT客户端未初始化")
            return

        try:
            payload = json.dumps(message)
            result = MessageHandler.mqtt_client.publish(topic, payload)
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"MQTT消息发送成功: {topic}")
            else:
                logger.error(f"MQTT消息发送失败: {result.rc}")
        except Exception as e:
            logger.error(f"MQTT消息发送错误: {str(e)}")

    def _send_response(
        self,
        request: Dict,  # 原始请求消息
        new_data: Dict,  # 新的数据（会覆盖原始消息中的同名字段）
        response_topic: str,  # 可选的响应主题
    ) -> bool:
        """
        发送响应消息，直接合并原始消息和新数据

        Args:
            request: 原始请求消息（完整保留）
            new_data: 新的数据（会覆盖原始消息中的同名字段）
            response_topic: 可选的响应主题
        """
        # 直接复制原始消息
        response = request.copy()

        # 更新新的数据（直接覆盖）
        response.update(new_data)

        # 确保时间戳更新（如果新数据中没有指定）
        if "timestamp" not in new_data:
            response["timestamp"] = int(time.time())

        # 检查是否使用MQTT发送给无人机
        # if settings.settings.mqtt.enabled and response_topic == MessageHandler.uav_topic: # Corrected access
        if MessageHandler.is_shutting_down:
            logger.warning("服务正在关闭，停止发送消息")
            return False

        try:
            if settings.settings.mqtt.enabled:  # Corrected access
                self._send_mqtt(response_topic, response)
            else:
                # 使用Kafka发送
                if MessageHandler.producer:
                    MessageHandler.producer.send(response_topic, response)
                    MessageHandler.producer.flush()
                    logger.info(f"已通过Kafka发送响应到{response_topic}: {response}")
                else:
                    logger.warning("Kafka生产者未初始化或已关闭")
                    return False
            return True
        except Exception as e:
            logger.warning(f"发送响应消息失败: {str(e)}")
            return False

    def _clear_all_routes(self):
        """清空所有航线数据 - 使用数据库连接管理器"""
        # 确保数据库连接管理器已初始化
        if MessageHandler.db_manager is None:
            MessageHandler.db_manager = DBConnectionManager()

        # 检查连接是否可用
        if not self._check_db_conn():
            logger.error("数据库连接不可用，无法清空数据")
            return False

        def db_operation():
            # 获取连接
            conn = MessageHandler.db_manager.get_connection()

            with conn.cursor() as cursor:
                # 清空路径点表
                cursor.execute("TRUNCATE TABLE route_node_manage")
                # 清空航线表
                cursor.execute("TRUNCATE TABLE flight_route_manage")

            conn.commit()
            logger.info("成功清空所有航线数据")
            return True

        try:
            # 执行数据库操作
            return self._execute_with_retry(db_operation)
        except Exception as e:
            logger.error(f"清空航线数据失败: {str(e)}")
            return False

    def _execute_with_retry(self, operation):
        """执行数据库操作，带有增强的重试机制 - 使用数据库连接管理器"""
        # 确保数据库连接管理器已初始化
        if MessageHandler.db_manager is None:
            MessageHandler.db_manager = DBConnectionManager()

        # 委托给连接管理器执行操作
        return MessageHandler.db_manager.execute_with_retry(operation)

    @classmethod
    def initialize_connections(cls):
        """在系统启动时初始化所有连接"""
        # 初始化数据库连接管理器
        if cls.db_manager is None:
            try:
                # 创建数据库连接管理器实例
                cls.db_manager = DBConnectionManager()
                logger.info("系统启动时初始化数据库连接管理器成功")

                # 从数据库加载禁飞区
                if cls.map is not None:
                    try:
                        # 创建临时实例以调用实例方法
                        temp_instance = cls(cls.map, cls.occupancy_map)
                        temp_instance.load_no_fly_zones_from_db()
                    except Exception as load_error:
                        logger.error(f"系统启动时加载禁飞区失败: {str(load_error)}")
            except Exception as e:
                logger.error(f"系统启动时初始化数据库连接管理器失败: {str(e)}")
                cls.db_manager = None

        # 初始化MQTT客户端
        if (
            settings.settings.mqtt.enabled and cls.mqtt_client is None
        ):  # Corrected access
            try:
                cls.mqtt_client = mqtt.Client()
                cls.mqtt_client.username_pw_set(
                    settings.settings.mqtt.username,
                    settings.settings.mqtt.password,  # Corrected access
                )
                # 解析broker地址和端口
                host, port = settings.settings.mqtt.broker.split(
                    ":"
                )  # Corrected access
                cls.mqtt_client.connect(host, int(port), 60)
                cls.mqtt_client.loop_start()
                logger.info("系统启动时初始化MQTT客户端成功")
            except Exception as e:
                logger.error(f"系统启动时初始化MQTT客户端失败: {str(e)}")
                cls.mqtt_client = None

    @classmethod
    def close_kafka_producer(cls):
        """关闭Kafka生产者"""
        if cls.producer:
            try:
                cls.producer.flush()  # 确保所有消息都被发送
                cls.producer.close()
                cls.producer = None
                logger.info("Kafka生产者已关闭")
            except Exception as e:
                logger.warning(f"关闭Kafka生产者时出现警告: {str(e)}")

    @classmethod
    def reload_no_fly_zones(cls):
        """手动重新加载禁飞区"""
        if cls.map is None or cls.db_manager is None:
            logger.warning("地图或数据库连接管理器未初始化，无法加载禁飞区")
            return False

        try:
            # 创建临时实例以调用实例方法
            temp_instance = cls(cls.map, cls.occupancy_map)
            temp_instance.load_no_fly_zones_from_db()
            return True
        except Exception as e:
            logger.error(f"手动重新加载禁飞区失败: {str(e)}")
            return False

    @classmethod
    def close_connections(cls):
        """关闭所有连接和定时器"""
        # 设置关闭标志
        cls.is_shutting_down = True

        # 1. 关闭数据库连接管理器
        if cls.db_manager:
            try:
                cls.db_manager.close_connections()
                cls.db_manager = None
                logger.info("数据库连接管理器已关闭")
            except Exception as e:
                logger.warning(f"关闭数据库连接管理器失败: {str(e)}")

        # 2. 关闭消息相关连接（Kafka和MQTT）
        # 关闭Kafka生产者
        cls.close_kafka_producer()

        # 关闭MQTT客户端
        if cls.mqtt_client:
            try:
                cls.mqtt_client.loop_stop()
                cls.mqtt_client.disconnect()
                cls.mqtt_client = None
                logger.info("MQTT客户端已关闭")
            except Exception as e:
                logger.warning(f"关闭MQTT客户端失败: {str(e)}")

        # 3. 关闭可视化器
        if cls.visualizer:
            try:
                cls.visualizer.close()
                cls.visualizer = None
                logger.info("路径可视化器已关闭")
            except Exception as e:
                logger.warning(f"关闭路径可视化器失败: {str(e)}")

        # 等待一小段时间，确保所有资源都已释放
        time.sleep(0.5)

    def _save_to_db(
        self,
        route_id: int,
        route_name: str,
        estimated_time: int,
        all_distance: int,
        path_geo: List[Dict],
        is_update: bool = False,
        batch_size: int = 10,  # 进一步减小批处理大小，更频繁地释放连接
    ):
        """使用持久连接保存数据到数据库 - 使用数据库连接管理器"""
        # 确保数据库连接管理器已初始化
        if MessageHandler.db_manager is None:
            MessageHandler.db_manager = DBConnectionManager()

        # 创建一个字典来存储状态，这样内部函数可以修改它
        state = {"is_update": is_update}

        def db_operation():
            # 创建一个新的连接，而不是使用共享的连接
            # 这样可以确保操作完成后连接被完全释放
            conn_config = {
                "host": settings.settings.database.host,
                "port": settings.settings.database.port,
                "user": settings.settings.database.user,
                "password": settings.settings.database.password,
                "database": settings.settings.database.database,
                "autocommit": False,  # 开始时关闭自动提交
                "buffered": True,
                "use_pure": True,
                "connection_timeout": 30,
                "connect_timeout": 15,
            }

            # 使用with语句确保连接在操作完成后自动关闭
            # 创建一个新的连接，不使用连接池
            with mysql.connector.connect(**conn_config) as conn:
                try:
                    with conn.cursor() as cursor:
                        # 首先检查记录是否已存在
                        cursor.execute(
                            "SELECT COUNT(*) FROM flight_route_manage WHERE id = %s",
                            (route_id,),
                        )
                        record_exists = cursor.fetchone()[0] > 0

                        # 如果记录存在且不是更新操作，记录日志并返回
                        if record_exists and not state["is_update"]:
                            logger.warning(
                                f"航线ID {route_id} 已存在，但未标记为更新操作。将强制执行更新操作。"
                            )
                            state["is_update"] = True

                        # 如果是更新操作或记录已存在，先删除旧数据
                        if state["is_update"] or record_exists:
                            logger.info(f"删除航线ID {route_id} 的现有数据")
                            cursor.execute(
                                "DELETE FROM route_node_manage WHERE flight_route_id = %s",
                                (route_id,),
                            )
                            cursor.execute(
                                "DELETE FROM flight_route_manage WHERE id = %s",
                                (route_id,),
                            )

                        # 插入航线管理记录
                        cursor.execute(
                            "INSERT INTO flight_route_manage "
                            "(id, name, estimated_time, all_distance) "
                            "VALUES (%s, %s, %s, %s)",
                            (route_id, route_name, estimated_time, all_distance),
                        )

                        # 提交航线管理记录
                        conn.commit()

                        # 准备批量插入数据
                        batch_data = [
                            (
                                route_id,
                                point["index"],
                                point["lng"],
                                point["lat"],
                                point["height"],
                            )
                            for point in path_geo
                        ]

                        total_points = len(batch_data)
                        total_batches = (total_points - 1) // batch_size + 1

                        # 分批处理
                        for i in range(0, total_points, batch_size):
                            current_batch = batch_data[i : i + batch_size]
                            try:
                                cursor.executemany(
                                    "INSERT INTO route_node_manage "
                                    "(flight_route_id, node_index, longitude, latitude, height) "
                                    "VALUES (%s, %s, %s, %s, %s)",
                                    current_batch,
                                )
                                # 每批次提交一次，避免单个事务过大
                                conn.commit()
                                batch_num = i // batch_size + 1
                                logger.debug(
                                    f"已保存批次 {batch_num}/{total_batches}，进度: {min(i+batch_size, total_points)}/{total_points}"
                                )
                            except Exception as batch_error:
                                # 如果批处理失败，回滚并记录错误
                                conn.rollback()
                                logger.error(
                                    f"批量插入数据失败 (批次 {batch_num}/{total_batches}): {str(batch_error)}"
                                )
                                # 重新抛出异常以触发重试机制
                                raise

                        logger.info(
                            f"成功保存路径数据到自动路径规划数据库，route_id: {route_id}，共 {total_points} 个点"
                        )
                        return True

                except Exception as e:
                    # 发生异常时回滚事务
                    conn.rollback()
                    logger.error(f"数据库操作异常，已回滚: {str(e)}")
                    raise

        try:
            # 执行数据库操作，使用独立连接
            result = self._execute_with_retry(db_operation)
            return result
        except Exception as e:
            logger.error(f"保存路径数据失败: {str(e)}")
            return False
