// 三维栅格节点类定义
#pragma once

#include <limits> // 引入std::numeric_limits用于表示无穷大值
#include <memory> // 引入std::shared_ptr

class GridNode3D
{
public:
    // 节点在栅格中的位置（浮点数坐标）
    float x; // 经度方向坐标 (对应X轴/东西方向)
    float y; // 纬度方向坐标 (对应Y轴/南北方向)
    float z; // 高度方向坐标 (对应Z轴/垂直方向)

    // 时间戳（Unix时间戳格式，单位：秒）
    long long t; // 使用long long类型存储

    // 路径规划的代价值
    float g; // 从起始节点到当前节点的实际移动代价
    float h; // 从当前节点到目标节点的估计代价（启发式值）
    float f; // 总代价值，f = g + h

    // 指向路径中上一个节点的指针
    std::shared_ptr<GridNode3D> parent; // 使用 std::shared_ptr

    // 跳点搜索算法的专用参数
    int jump_step;   // 搜索时的跳跃步长
    bool need_sight; // 是否需要进行视线可达性检查

    // 节点构造函数
    GridNode3D(float x_coord, float y_coord, float z_coord,
               long long timestamp = 0LL,                             // 时间戳，默认为0
               float g_cost = std::numeric_limits<float>::infinity(), // 初始g值设为无穷大
               float h_cost = 0.0f,                                   // 初始h值设为0
               std::shared_ptr<GridNode3D> p_node = nullptr,          // 父节点指针，默认为空
               int j_step = 5,                                        // 默认跳跃步长为5
               bool n_sight = true)                                   // 默认不进行视线检查
        : x(x_coord), y(y_coord), z(z_coord),
          t(timestamp),
          g(g_cost), h(h_cost), f(g_cost + h_cost), // 初始化总代价f为g和h的和
          parent(p_node),                           // 直接使用传入的 shared_ptr
          jump_step(j_step),
          need_sight(n_sight)
    {
    }

    // 更新节点的总代价值f（在g值或h值发生变化时调用）
    void update_f_cost()
    {
        f = g + h;
    }

    // 定义小于运算符，用于在优先队列等容器中比较节点
    bool operator<(const GridNode3D &other) const
    {
        // 首先比较f值
        if (f != other.f)
            return f < other.f;
        // 如果f值相同，比较g值
        if (g != other.g)
            return g < other.g;
        // 如果g值也相同，比较坐标
        if (x != other.x)
            return x < other.x;
        if (y != other.y)
            return y < other.y;
        return z < other.z;
    }

    // 定义相等运算符
    bool operator==(const GridNode3D &other) const
    {
        return x == other.x && y == other.y && z == other.z;
    }
};
